# AdMesh Dashboard - Development Guide

The AdMesh Dashboard is a Next.js application that provides the frontend interface for the AdMesh platform.

## 🏗️ Architecture

- **Framework:** Next.js 15 with React 19
- **Styling:** Tailwind CSS with shadcn/ui components
- **Authentication:** Firebase Auth
- **State Management:** React hooks and context
- **API Communication:** Fetch API with SWR for caching

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation
```bash
cd admesh-dashboard
npm install
```

### Development
```bash
# Development mode (uses admesh-dev Firebase)
npm run dev

# Test mode (uses production Firebase with localhost API)
npm run dev:test

# Production config (uses production Firebase and API)
npm run dev:prod
```

## 🌍 Environment Configuration

### Environment Detection
The app automatically detects the environment based on:
1. `NEXT_PUBLIC_ENVIRONMENT` environment variable
2. `NODE_ENV` for fallback
3. Vercel environment detection

### Environment Files
```
.env.development     # Development configuration
.env.test           # Test environment configuration  
.env.production     # Production configuration
```

### Key Environment Variables
```bash
# Environment identifier
NEXT_PUBLIC_ENVIRONMENT=development|test|production

# API endpoint
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:8000

# Firebase configuration
NEXT_PUBLIC_FIREBASE_PROJECT_ID=admesh-dev
NEXT_PUBLIC_FIREBASE_API_KEY=...
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=...

# External services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── dashboard/         # Dashboard pages
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── auth/             # Authentication components
│   ├── dashboard/        # Dashboard-specific components
│   └── common/           # Common components
├── config/               # Configuration
│   └── environment.ts    # Environment configuration
├── contexts/             # React contexts
├── hooks/                # Custom hooks
├── lib/                  # Utility libraries
│   ├── firebase.ts       # Firebase configuration
│   ├── api.ts           # API client
│   └── utils.ts         # Utility functions
├── types/                # TypeScript type definitions
└── constants/            # Application constants
```

## 🔧 Development Workflow

### 1. Start Development Server
```bash
npm run dev
```
- Runs on `http://localhost:3000`
- Hot reload enabled
- Uses development Firebase project

### 2. Environment-Specific Development
```bash
# Test with production data
npm run dev:test

# Test production configuration
npm run dev:prod
```

### 3. Building
```bash
# Build for production
npm run build

# Build for specific environment
npm run build:test
npm run build:prod
```

### 4. Linting and Code Quality
```bash
# Run ESLint
npm run lint

# Fix linting issues
npm run lint -- --fix
```

## 🔥 Firebase Integration

### Configuration
Firebase configuration is handled in `src/lib/firebase.ts` and automatically switches based on environment:

- **Development:** Uses `admesh-dev` project
- **Test:** Uses `admesh-9560c` project (production)
- **Production:** Uses `admesh-9560c` project

### Authentication
```typescript
import { auth } from '@/lib/firebase';
import { signInWithEmailAndPassword } from 'firebase/auth';

// Sign in user
const signIn = async (email: string, password: string) => {
  return await signInWithEmailAndPassword(auth, email, password);
};
```

### Firestore
```typescript
import { db } from '@/lib/firebase';
import { collection, getDocs } from 'firebase/firestore';

// Get data from Firestore
const getData = async () => {
  const snapshot = await getDocs(collection(db, 'collection-name'));
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};
```

## 🌐 API Integration

### API Client
The API client is configured in `src/lib/api.ts` and automatically uses the correct endpoint based on environment.

### Making API Calls
```typescript
import { apiClient } from '@/lib/api';

// GET request
const data = await apiClient.get('/endpoint');

// POST request
const result = await apiClient.post('/endpoint', { data });

// With authentication
const authData = await apiClient.get('/protected-endpoint', {
  headers: {
    Authorization: `Bearer ${token}`
  }
});
```

### SWR Integration
```typescript
import useSWR from 'swr';
import { apiClient } from '@/lib/api';

const fetcher = (url: string) => apiClient.get(url);

export function useData() {
  const { data, error, isLoading } = useSWR('/api/data', fetcher);
  
  return {
    data,
    isLoading,
    isError: error
  };
}
```

## 🎨 Styling and Components

### Tailwind CSS
The project uses Tailwind CSS for styling with a custom configuration:

```javascript
// tailwind.config.mjs
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Custom color palette
      }
    }
  }
};
```

### shadcn/ui Components
Pre-built components are available in `src/components/ui/`:

```typescript
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';

export function MyComponent() {
  return (
    <Card>
      <Input placeholder="Enter text" />
      <Button>Submit</Button>
    </Card>
  );
}
```

### Custom Components
Create reusable components in `src/components/`:

```typescript
// src/components/common/LoadingSpinner.tsx
export function LoadingSpinner() {
  return (
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
  );
}
```

## 🔒 Authentication Flow

### Protected Routes
```typescript
// src/components/auth/ProtectedRoute.tsx
import { useAuth } from '@/contexts/AuthContext';
import { redirect } from 'next/navigation';

export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  
  if (loading) return <LoadingSpinner />;
  if (!user) redirect('/sign-in');
  
  return <>{children}</>;
}
```

### Auth Context
```typescript
// src/contexts/AuthContext.tsx
import { createContext, useContext } from 'react';
import { User } from 'firebase/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | null>(null);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}
```

## 🧪 Testing

### Running Tests
```bash
# Run integration tests
npm run test-integration

# Run specific test
node test-integration.js
```

### Test Environment
The test environment uses:
- Production Firebase for authentication
- Localhost API for backend calls
- Real data for realistic testing

## 📊 Performance Optimization

### Next.js Features
- **App Router:** Modern routing with layouts
- **Server Components:** Reduced client-side JavaScript
- **Image Optimization:** Automatic image optimization
- **Code Splitting:** Automatic code splitting

### Bundle Analysis
```bash
# Analyze bundle size
npm run build
npx @next/bundle-analyzer
```

## 🚨 Troubleshooting

### Common Issues

#### Environment Variables Not Loading
```bash
# Check environment file exists
ls -la .env*

# Verify variables are set
echo $NEXT_PUBLIC_ENVIRONMENT
```

#### API Connection Failed
```bash
# Verify backend is running
curl http://127.0.0.1:8000/health

# Check API URL in environment
grep NEXT_PUBLIC_API_BASE_URL .env*
```

#### Firebase Configuration Error
```bash
# Check Firebase project ID
grep NEXT_PUBLIC_FIREBASE_PROJECT_ID .env*

# Verify Firebase config in browser console
```

#### Build Errors
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

### Debug Mode
Enable debug logging by setting:
```bash
NEXT_PUBLIC_DEBUG=true
```

## 🔄 Deployment

### Vercel Deployment
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Manual Deployment
```bash
# Build for production
npm run build:prod

# Start production server
npm start
```

## 📚 Additional Resources

- **Next.js Documentation:** https://nextjs.org/docs
- **Tailwind CSS:** https://tailwindcss.com/docs
- **shadcn/ui:** https://ui.shadcn.com
- **Firebase:** https://firebase.google.com/docs
- **SWR:** https://swr.vercel.app
