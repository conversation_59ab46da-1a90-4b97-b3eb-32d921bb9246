{"name": "admesh-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:test": "NEXT_PUBLIC_ENVIRONMENT=test next dev", "dev:prod": "NEXT_PUBLIC_ENVIRONMENT=production next dev", "build": "next build", "build:test": "NEXT_PUBLIC_ENVIRONMENT=test next build", "build:prod": "NEXT_PUBLIC_ENVIRONMENT=production next build", "start": "next start", "lint": "next lint", "generate-og": "node scripts/generate-og-images.js", "test-integration": "node test-integration.js"}, "dependencies": {"@clerk/nextjs": "^6.19.3", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@react-icons/all-files": "^4.1.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/path-polygon": "^3.8.1", "@tsparticles/react": "^3.0.0", "@vercel/analytics": "^1.5.0", "@vercel/og": "^0.6.8", "@vercel/speed-insights": "^1.2.0", "admesh-ui-sdk": "^0.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "firebase": "^11.6.0", "framer-motion": "^12.6.3", "lucide-react": "^0.487.0", "marked": "^15.0.8", "next": "15.2.4", "next-share": "^0.27.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tailwind-variants": "^1.0.0", "tsparticles": "^3.8.1", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8.5.3", "tailwindcss": "^4.1.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}