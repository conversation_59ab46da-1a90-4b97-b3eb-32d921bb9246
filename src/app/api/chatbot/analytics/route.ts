import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { event, properties } = body;

    // Get client information
    const clientIP = request.headers.get("x-forwarded-for") || 
                    request.headers.get("x-real-ip") || 
                    "unknown";
    
    const userAgent = request.headers.get("user-agent") || "unknown";

    // Prepare analytics data
    const analyticsData = {
      event,
      properties: {
        ...properties,
        ip: clientIP,
        user_agent: userAgent,
        timestamp: new Date().toISOString(),
        session_id: properties.session_id || "unknown"
      }
    };

    // Log analytics event
    console.log("Chatbot Analytics Event:", analyticsData);

    // Here you can integrate with your analytics service
    // Examples: Google Analytics, Mixpanel, Amplitude, etc.
    
    // For now, we'll just log and store basic metrics
    try {
      // You can add database storage for analytics here
      // Example: Store in Firestore, PostgreSQL, etc.
      
      // Send to external analytics service if needed
      // await sendToAnalyticsService(analyticsData);
      
    } catch (storageError) {
      console.error("Error storing analytics:", storageError);
    }

    return NextResponse.json(
      { 
        success: true, 
        message: "Analytics event tracked" 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Error tracking analytics:", error);
    return NextResponse.json(
      { error: "Failed to track analytics" },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
