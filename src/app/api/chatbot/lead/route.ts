import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, page, context, timestamp } = body;

    // Validate required fields
    if (!email || !email.includes("@")) {
      return NextResponse.json(
        { error: "Valid email is required" },
        { status: 400 }
      );
    }

    // Get client IP for analytics
    const clientIP = request.headers.get("x-forwarded-for") || 
                    request.headers.get("x-real-ip") || 
                    "unknown";

    // Prepare email content for notification (for future use)
    // const emailContent = `
    //   New Chatbot Lead from AdMesh Website
    //
    //   Email: ${email}
    //   Page: ${page}
    //   Context: ${context}
    //   Timestamp: ${timestamp}
    //   IP Address: ${clientIP}
    //   User Agent: ${request.headers.get("user-agent") || "unknown"}
    //
    //   This lead was generated through the AdMesh chatbot interaction.
    //   Please follow up within 24 hours.
    // `;

    // Send notification <NAME_EMAIL>
    try {
      // For now, we'll use a simple email service or log the notification
      // Since the backend email route requires authentication, we'll implement a simpler approach

      console.log("📧 EMAIL NOTIFICATION:", {
        to: "<EMAIL>",
        subject: `New Chatbot Lead: ${email} (${context})`,
        lead_details: {
          email,
          page,
          context,
          timestamp,
          ip: clientIP
        }
      });

      // You can integrate with a service like Resend directly here
      // or create a separate webhook endpoint for email notifications

      // Email HTML template (for future use when email service is integrated)
      // const emailHtml = `
      //       <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      //         <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
      //           🤖 New Chatbot Lead
      //         </h2>
      //
      //         <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      //           <h3 style="margin-top: 0; color: #007bff;">Contact Information</h3>
      //           <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
      //           <p><strong>Page:</strong> ${page}</p>
      //           <p><strong>Context:</strong> ${context}</p>
      //           <p><strong>Timestamp:</strong> ${new Date(timestamp).toLocaleString()}</p>
      //         </div>
      //
      //         <div style="background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0;">
      //           <h4 style="margin-top: 0;">Technical Details</h4>
      //           <p><strong>IP Address:</strong> ${clientIP}</p>
      //           <p><strong>User Agent:</strong> ${request.headers.get("user-agent") || "unknown"}</p>
      //         </div>
      //
      //         <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
      //           <h4 style="margin-top: 0; color: #155724;">Next Steps</h4>
      //           <ul style="color: #155724;">
      //             <li>Follow up within 24 hours</li>
      //             <li>Send personalized information based on context (${context})</li>
      //             <li>Offer to schedule a demo call</li>
      //             <li>Add to appropriate email sequence</li>
      //           </ul>
      //         </div>
      //
      //         <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
      //           <p style="color: #6c757d; font-size: 14px;">
      //             This notification was generated by the AdMesh chatbot system.<br>
      //             <a href="https://www.useadmesh.com" style="color: #007bff;">Visit AdMesh Dashboard</a>
      //           </p>
      //         </div>
      //       </div>
      //     `;

      // For production, you would integrate with your email service here
      // Example with Resend:
      /*
      const resend = new Resend(process.env.RESEND_API_KEY);
      // await resend.emails.send({
      //   from: 'AdMesh Chatbot <<EMAIL>>',
      //   to: ['<EMAIL>'],
      //   subject: `New Chatbot Lead: ${email} (${context})`,
      //   html: emailHtml,
      // });
      */

    } catch (emailError) {
      console.error("Error sending notification email:", emailError);
      // Don't fail the request if email fails
    }

    // Store lead in database (optional - you can add this later)
    try {
      // You can add database storage here if needed
      // For now, we'll just log it
      console.log("Chatbot lead captured:", {
        email,
        page,
        context,
        timestamp,
        ip: clientIP
      });
    } catch (dbError) {
      console.error("Error storing lead in database:", dbError);
      // Don't fail the request if database storage fails
    }

    // Track analytics event
    try {
      // Send analytics data to backend
      const analyticsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/analytics/track`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          event: "chatbot_lead_conversion",
          properties: {
            email,
            page,
            context,
            timestamp,
            ip: clientIP,
            user_agent: request.headers.get("user-agent") || "unknown"
          }
        }),
      });

      console.log("Analytics: Chatbot lead conversion", {
        email,
        page,
        context,
        timestamp,
        success: analyticsResponse.ok
      });
    } catch (analyticsError) {
      console.error("Error tracking analytics:", analyticsError);
    }

    return NextResponse.json(
      { 
        success: true, 
        message: "Lead captured successfully" 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Error processing chatbot lead:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
