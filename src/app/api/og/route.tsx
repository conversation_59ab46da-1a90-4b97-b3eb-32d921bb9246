import { NextRequest } from 'next/server';
import { ImageResponse } from 'next/og';

export const runtime = 'edge';

/**
 * Dynamic OG Image Generator for AdMesh
 *
 * This API route generates dynamic Open Graph images for different pages
 * using Vercel's OG Image Generation with Satori.
 *
 * Query parameters:
 * - title: The main title to display
 * - description: The description text
 * - type: The page type (default, brand, agent, user)
 * - mode: The color mode (dark, light)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;

    // Get dynamic params from request
    const title = searchParams.get('title') || 'AdMesh – Promote your products inside AI';
    const description = searchParams.get('description') || 'Explore tools, earn rewards, and power the future of monetized intent with AdMesh.';
    const type = searchParams.get('type') || 'default'; // default, brand, agent, user
    const mode = searchParams.get('mode') || 'dark'; // dark, light

    // We'll use system fonts for simplicity
    // Custom fonts can be added later if needed

    // Define colors based on type and mode
    let bgColor = '#000000';
    let textColor = '#FFFFFF';
    let secondaryTextColor = '#CCCCCC';
    let patternOpacity = 0.15;

    if (mode === 'light') {
      bgColor = '#FFFFFF';
      textColor = '#000000';
      secondaryTextColor = '#333333';
      patternOpacity = 0.1;
    }

    // Type-specific colors
    if (type === 'brand') {
      bgColor = mode === 'dark' ? '#111111' : '#F8F8F8';
    } else if (type === 'agent') {
      bgColor = mode === 'dark' ? '#0F172A' : '#F0F4F8';
    } else if (type === 'user') {
      bgColor = mode === 'dark' ? '#18181B' : '#FAFAFA';
    }

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: bgColor,
            backgroundImage:
              `radial-gradient(circle at 25px 25px, rgba(255, 255, 255, ${patternOpacity}) 2%, transparent 0%), ` +
              `radial-gradient(circle at 75px 75px, rgba(255, 255, 255, ${patternOpacity}) 2%, transparent 0%)`,
            backgroundSize: '100px 100px',
            color: textColor,
            padding: '40px',
            position: 'relative',
          }}
        >
          {/* Logo and Brand */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'absolute',
              top: 40,
              left: 40,
            }}
          >
            <div
              style={{
                width: 60,
                height: 60,
                borderRadius: '50%',
                border: `2px solid ${textColor}`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 16,
              }}
            >
              <svg width="40" height="40" viewBox="0 0 1024 1024" fill="none">
                <path fill={textColor} d="M512 0C229.23 0 0 229.23 0 512c0 282.77 229.23 512 512 512 282.77 0 512-229.23 512-512C1024 229.23 794.77 0 512 0zm0 930.91c-231.36 0-418.91-187.55-418.91-418.91S280.64 93.09 512 93.09 930.91 280.64 930.91 512 743.36 930.91 512 930.91z" />
              </svg>
            </div>
            <span style={{ fontSize: 32, fontWeight: 'bold' }}>AdMesh</span>
          </div>

          {/* Main Content */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              width: '100%',
              maxWidth: 900,
            }}
          >
            <h1
              style={{
                fontSize: 64,
                fontWeight: 'bold',
                margin: '0 0 20px',
                lineHeight: 1.2,

              }}
            >
              {title}
            </h1>
            <p
              style={{
                fontSize: 32,
                margin: 0,
                color: secondaryTextColor,
                maxWidth: 800,

              }}
            >
              {description}
            </p>
          </div>

          {/* URL at bottom */}
          <div
            style={{
              position: 'absolute',
              bottom: 40,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span style={{ fontSize: 24, color: secondaryTextColor }}>useadmesh.com</span>
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,

      },
    );
  } catch (e: unknown) {
    console.error(`Error generating OG image: ${e instanceof Error ? e.message : String(e)}`);
    return new Response(`Failed to generate image`, {
      status: 500,
    });
  }
}
