"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Package, Users, Building, Settings, ArrowRight, ClipboardList } from "lucide-react";
import Link from "next/link";

export default function AdminDashboardPage() {
  const { role } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  // Redirect if not admin or redirect to products page if admin
  useEffect(() => {
    if (role && role !== "admin") {
      router.push("/dashboard");
    } else if (role === "admin") {
      // Redirect admin to products page
      router.replace("/dashboard/admin/products");
    } else {
      setLoading(false);
    }
  }, [role, router]);

  // If not admin, show loading or redirect
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const adminCards = [
    {
      title: "Products",
      description: "Manage all products in the platform",
      icon: <Package className="h-8 w-8" />,
      href: "/dashboard/admin/products",
    },
    {
      title: "Users",
      description: "Manage users, roles and permissions",
      icon: <Users className="h-8 w-8" />,
      href: "/dashboard/admin/users",
    },
    {
      title: "Brands",
      description: "Manage brand accounts and offers",
      icon: <Building className="h-8 w-8" />,
      href: "/dashboard/admin/brands",
    },
    {
      title: "Waitlist",
      description: "Manage waitlist entries and early access",
      icon: <ClipboardList className="h-8 w-8" />,
      href: "/dashboard/admin/waitlist",
    },
    {
      title: "Settings",
      description: "Configure platform settings",
      icon: <Settings className="h-8 w-8" />,
      href: "/dashboard/admin/settings",
    },
  ];

  return (
    <div className="space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Admin Dashboard</h2>
        <p className="text-muted-foreground mt-1">
          Manage and monitor the AdMesh platform.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {adminCards.map((card) => (
          <Card key={card.title} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">{card.title}</CardTitle>
              {card.icon}
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-3">{card.description}</CardDescription>
              <Link href={card.href}>
                <Button variant="outline" className="w-full">
                  Manage
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
