"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import { Copy, Loader2, Key, AlertCircle } from "lucide-react";

interface ApiKey {
  id: string;
  key: string;
  type: string;
  created_at: number;
  last_used: number | null;
  is_active: boolean;
  name: string;
}

export default function ApiKeysPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [testKey, setTestKey] = useState<ApiKey | null>(null);
  const [productionKey, setProductionKey] = useState<ApiKey | null>(null);
  const [isGeneratingTestKey, setIsGeneratingTestKey] = useState(false);
  const [isGeneratingProductionKey, setIsGeneratingProductionKey] = useState(false);
  const [agentId, setAgentId] = useState("");
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "https://api.useadmesh.com";

  const fetchApiKeys = useCallback(async () => {
    setLoading(true);
    try {
      const token = await user?.getIdToken();
      if (!token) throw new Error("Not authenticated");

      // First get the agent data to get the agent_id
      const agentResponse = await fetch(`${apiBaseUrl}/agent/onboarding/data`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!agentResponse.ok) {
        throw new Error("Failed to fetch agent data");
      }

      const agentData = await agentResponse.json();
      setAgentId(agentData.agent_id || "");

      // Now get the API keys
      const keysResponse = await fetch(`${apiBaseUrl}/api-keys/list`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!keysResponse.ok) {
        throw new Error("Failed to fetch API keys");
      }

      const keysData = await keysResponse.json();

      // Filter active keys by type
      const activeKeys = keysData.keys.filter((key: ApiKey) => key.is_active);
      const testKeyData = activeKeys.find((key: ApiKey) => key.type === "test") || null;
      const productionKeyData = activeKeys.find((key: ApiKey) => key.type === "production") || null;

      setTestKey(testKeyData);
      setProductionKey(productionKeyData);
    } catch (error) {
      console.error("Error fetching API keys:", error);
      toast.error("Failed to load API keys");
    } finally {
      setLoading(false);
    }
  }, [user, apiBaseUrl]);

  useEffect(() => {
    if (!user) return;
    fetchApiKeys();
  }, [user, fetchApiKeys]);

  const generateApiKey = async (keyType: "test" | "production") => {
    const isTest = keyType === "test";
    if (isTest) {
      setIsGeneratingTestKey(true);
    } else {
      setIsGeneratingProductionKey(true);
    }

    try {
      const token = await user?.getIdToken();
      if (!token) throw new Error("Not authenticated");

      const response = await fetch(`${apiBaseUrl}/api-keys/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          agent_id: agentId,
          type: keyType,
          name: `${keyType.charAt(0).toUpperCase() + keyType.slice(1)} API Key`
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `Failed to generate ${keyType} API key`);
      }

      const keyData = await response.json();

      if (isTest) {
        setTestKey(keyData);
        toast.success("Test API key generated successfully");
      } else {
        setProductionKey(keyData);
        toast.success("Production API key generated successfully");
      }

      // Update agent document with the API key ID
      await fetch(`${apiBaseUrl}/agent/update-api-key`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          agent_id: agentId,
          key_type: keyType,
          key_id: keyData.id
        })
      });
    } catch (error) {
      console.error(`Error generating ${keyType} API key:`, error);
      toast.error(`Failed to generate ${keyType} API key: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      if (isTest) {
        setIsGeneratingTestKey(false);
      } else {
        setIsGeneratingProductionKey(false);
      }
    }
  };

  const formatDate = (timestamp: number | null) => {
    if (!timestamp) return "Never";
    return new Date(timestamp * 1000).toLocaleString();
  };

  const ApiKeyDisplay = ({ keyData, keyType }: { keyData: ApiKey | null, keyType: "test" | "production" }) => {
    return (
      <div className="space-y-4">
        {keyData ? (
          <>
            <div className="space-y-2">
              <Label>API Key</Label>
              <div className="relative">
                <Input
                  value={keyData.key}
                  readOnly
                  className="h-12 pr-20 font-mono text-sm"
                />
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute right-2 top-2"
                  onClick={() => {
                    navigator.clipboard.writeText(keyData.key);
                    toast.success(`${keyType === "test" ? "Test" : "Production"} API Key copied to clipboard`);
                  }}
                >
                  <Copy className="h-4 w-4 mr-1" /> Copy
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label className="text-xs text-muted-foreground">Created</Label>
                <p>{formatDate(keyData.created_at)}</p>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Last Used</Label>
                <p>{formatDate(keyData.last_used)}</p>
              </div>
            </div>
            <Button
              onClick={() => generateApiKey(keyType)}
              disabled={keyType === "test" ? isGeneratingTestKey : isGeneratingProductionKey}
              variant="outline"
              className="w-full"
            >
              {keyType === "test" && isGeneratingTestKey || keyType === "production" && isGeneratingProductionKey ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                </>
              ) : (
                <>Regenerate {keyType === "test" ? "Test" : "Production"} API Key</>
              )}
            </Button>
          </>
        ) : (
          <>
            <div className="text-center py-8">
              <Key className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No {keyType === "test" ? "Test" : "Production"} API Key</h3>
              <p className="text-muted-foreground mb-4">
                Generate a {keyType === "test" ? "test" : "production"} API key to integrate with AdMesh.
              </p>
              <Button
                onClick={() => generateApiKey(keyType)}
                disabled={keyType === "test" ? isGeneratingTestKey : isGeneratingProductionKey}
                className="w-full"
              >
                {keyType === "test" && isGeneratingTestKey || keyType === "production" && isGeneratingProductionKey ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                  </>
                ) : (
                  <>Generate {keyType === "test" ? "Test" : "Production"} API Key</>
                )}
              </Button>
            </div>
          </>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container max-w-4xl mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">API Keys</h1>

      <Tabs defaultValue="sandbox" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger value="sandbox">Sandbox</TabsTrigger>
          <TabsTrigger value="production">Production</TabsTrigger>
        </TabsList>

        <TabsContent value="sandbox">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" /> Test API Key
              </CardTitle>
              <CardDescription>
                Use this key for testing. It won&apos;t affect your production data.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApiKeyDisplay keyData={testKey} keyType="test" />

              <Alert className="mt-6" variant="warning">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Test Environment</AlertTitle>
                <AlertDescription>
                  Test keys are for development purposes only. Any data created with test keys will be marked as test data.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="production">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" /> Production API Key
              </CardTitle>
              <CardDescription>
                Use this key for your production environment. Keep it secure.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApiKeyDisplay keyData={productionKey} keyType="production" />

              <Alert className="mt-6" variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Production Environment</AlertTitle>
                <AlertDescription>
                  This key has full access to your account. Never share it publicly or include it in client-side code.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
