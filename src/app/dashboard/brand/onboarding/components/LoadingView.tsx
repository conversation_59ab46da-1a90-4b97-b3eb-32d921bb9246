"use client";

import { Loader2 } from "lucide-react";

const LoadingView = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-background to-background/95 dark:from-background dark:to-background/95">
      <div className="p-8 rounded-xl bg-card/80 dark:bg-card/50 backdrop-blur-md shadow-xl border border-border/30 flex flex-col items-center">
        <div className="relative mb-6">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <div className="absolute inset-0 rounded-full animate-ping bg-primary/20 blur-sm"></div>
        </div>
        <p className="text-muted-foreground font-medium">Loading your onboarding progress...</p>
      </div>
    </div>
  );
};

export default LoadingView;
