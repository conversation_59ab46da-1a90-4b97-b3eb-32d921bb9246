"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { dollarsToCents } from "@/lib/utils";
import {
  handleTestConversion as handleTestConversionUtil,
  checkTestConversions as checkTestConversionsUtil
} from "@/lib/conversion-utils";

import StepIndicator from "./StepIndicator";
import BrandStep from "./steps/BrandStep";
import ProductStep from "./steps/ProductStep";
import OfferStep from "./steps/OfferStep";
import TrackingStep from "./steps/TrackingStep";
import SuccessView from "./SuccessView";
import LoadingView from "./LoadingView";
import { Offer } from "@/types/onboarding";

const OnboardingContainer = () => {
  const router = useRouter();
  const { user } = useAuth();

    // Step state
  const [step, setStep] = useState<number>(1);
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState(true);

  // Error state
  const [errors, setErrors] = useState<{
    brand: Record<string, string>;
    product: Record<string, string>;
    offer: Record<string, string>;
    tracking: Record<string, string>;
  }>({
    brand: {},
    product: {},
    offer: {},
    tracking: {}
  });

  // Onboarding status
  const [onboardingStatus, setOnboardingStatus] = useState<{
    onboarding_status: string;
    steps: {
      brand: boolean;
      product: boolean;
      offer: boolean;
      tracking: boolean;
    };
    next_step: string | null;
  } | null>(null);

  // IDs for each step
  const [brandId, setBrandId] = useState<string | null>(null);
  const [productId, setProductId] = useState<string | null>(null);
  const [offerId, setOfferId] = useState<string | null>(null);
  const [productExisted, setProductExisted] = useState(false);

  // Step-specific loading states
  const [brandLoading, setBrandLoading] = useState(false);
  const [productLoading, setProductLoading] = useState(false);
  const [offerLoading, setOfferLoading] = useState(false);

  // Test conversion states
  const [isTestingConversion, setIsTestingConversion] = useState(false);
  const [isCheckingConversions, setIsCheckingConversions] = useState(false);
  const [testConversionExists, setTestConversionExists] = useState(false);

  // Form data
  const [brand, setBrand] = useState({
    website: "",
    brand_name: "",
    logo_url: "",
    work_email: "",
    headquarters: "",
    application_type: "website", // Default to website
  });

  const [product, setProduct] = useState({
    title: "",
    url: "",
    description: "",
    categories: [] as string[], // Changed from single category to multiple categories
    keywords: [] as string[],
    pricing_url: "",
    audience_segment: "",
    integration_list: [] as string[],
    active_offers: [] as string[],
    inactive_offers: [] as string[]
  });

  const [offer, setOffer] = useState<Offer>({
    goal: "signup", // Default to signup
    model: "CPA", // Default to CPA model
    payout_amount: "0.00", // Start with zero payout amount
    budget: "0.00", // Start with zero budget
    promo_applied: false, // Flag to track if promo credit has been applied
    offer_incentive: undefined
  });

  const [tracking, setTracking] = useState(() => {
    // Initialize with default values
    const defaultTracking = {
      method: "redirect_pixel", // default method
      webhook_url: "", // only for server-side API
      notes: "", // any additional notes for manual approval
      redirect_url: "", // destination URL for redirect_pixel method
      target_urls: [] as string[], // list of target URLs
    };

    // Check if we have a saved redirect URL in local storage
    if (typeof window !== 'undefined') {
      const REDIRECT_URL_STORAGE_KEY = 'admesh_redirect_url';
      const savedRedirectUrl = localStorage.getItem(REDIRECT_URL_STORAGE_KEY);
      if (savedRedirectUrl) {
        return {
          ...defaultTracking,
          redirect_url: savedRedirectUrl
        };
      }
    }

    return defaultTracking;
  });

  // Function to force step change and scroll to top
  const forceStepChange = (newStep: number) => {
    setStep(newStep);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Validate brand fields
  const validateBrand = () => {
    const brandErrors: Record<string, string> = {};
    if (!brand.website) brandErrors.website = "Website is required";
    if (!brand.brand_name) brandErrors.brand_name = "Brand name is required";
    if (!brand.work_email) brandErrors.work_email = "Work email is required";
    if (!brand.application_type) brandErrors.application_type = "Application type is required";

    // URL format validation for website
    if (brand.website && !brand.website.match(/^(https?:\/\/)?(www\.)?[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z0-9-\.]+/)) {
      brandErrors.website = "Please enter a valid website URL";
    }

    // Email format validation for work email
    if (brand.work_email && !brand.work_email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      brandErrors.work_email = "Please enter a valid email address";
    }

    setErrors(prev => ({ ...prev, brand: brandErrors }));
    return Object.keys(brandErrors).length === 0;
  };

  // Validate product fields
  const validateProduct = () => {
    const productErrors: Record<string, string> = {};
    if (!product.title) productErrors.title = "Product title is required";
    if (!product.url) productErrors.url = "Product URL is required";
    if (!product.description) productErrors.description = "Description is required";
    if (!product.categories || product.categories.length === 0) {
      productErrors.categories = "At least one category is required";
    }

    setErrors(prev => ({ ...prev, product: productErrors }));
    return Object.keys(productErrors).length === 0;
  };

  // Validate offer fields
  const validateOffer = () => {
    const offerErrors: Record<string, string> = {};
    if (!offer.goal) offerErrors.goal = "Conversion goal is required";
    if (!offer.model) offerErrors.model = "Payout model is required";

    // Check if promo has been applied
    if (offer.promo_applied) {
      // Validate that values are set correctly when promo is applied
      if (!offer.payout_amount || parseFloat(offer.payout_amount) <= 0) {
        offerErrors.payout_amount = "Payout amount must be set when promo credit is applied";
      }

      if (!offer.budget || parseFloat(offer.budget) <= 0) {
        offerErrors.budget = "Budget must be set when promo credit is applied";
      }
    } else {
      // If promo is not applied, validate the fields normally
      // Validate payout amount
      if (!offer.payout_amount) {
        offerErrors.payout_amount = "Payout amount is required";
      } else {
        const payoutAmount = parseFloat(offer.payout_amount);
        if (isNaN(payoutAmount)) {
          offerErrors.payout_amount = "Payout amount must be a valid number";
        } else if (payoutAmount <= 0) {
          offerErrors.payout_amount = "Payout amount must be greater than 0";
        } else if (payoutAmount > 1000) {
          offerErrors.payout_amount = "Payout amount cannot exceed $1000";
        }
      }

      // Validate budget
      if (!offer.budget) {
        offerErrors.budget = "Budget is required";
      } else {
        const budget = parseFloat(offer.budget);
        if (isNaN(budget)) {
          offerErrors.budget = "Budget must be a valid number";
        } else if (budget <= 0) {
          offerErrors.budget = "Budget must be greater than 0";
        } else if (budget > 10000) {
          offerErrors.budget = "Budget cannot exceed $10,000";
        }
      }

      // Validate that budget is greater than or equal to payout amount
      if (!offerErrors.payout_amount && !offerErrors.budget) {
        const payoutAmount = parseFloat(offer.payout_amount);
        const budget = parseFloat(offer.budget);
        if (budget < payoutAmount) {
          offerErrors.budget = "Budget must be greater than or equal to payout amount";
        }
      }

      // If fields are empty or zero, remind user to apply promo credit
      if (parseFloat(offer.payout_amount) <= 0 || parseFloat(offer.budget) <= 0) {
        offerErrors.promo = "Please apply promo credit to set default values";
      }
    }

    console.log("Offer validation errors:", offerErrors);
    setErrors(prev => ({ ...prev, offer: offerErrors }));
    return Object.keys(offerErrors).length === 0;
  };

  // Validate tracking fields
  const validateTracking = () => {
    const trackingErrors: Record<string, string> = {};
    if (!tracking.redirect_url) {
      trackingErrors.redirect_url = "Redirect URL is required";
    }

    setErrors(prev => ({ ...prev, tracking: trackingErrors }));
    return Object.keys(trackingErrors).length === 0;
  };

  // Fetch website information
  const fetchWebsiteInfo = useCallback(async (website: string) => {
    if (!user || !website) {
      toast.error("Please enter a website");
      return false;
    }

    setBrandLoading(true);
    try {
      const token = await user.getIdToken();

      // Check if the response is JSON
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/api/onboard/intel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ website }),
      });

      if (!res.ok) {
        throw new Error("Failed to fetch website information");
      }

      // Check if the response is JSON
      const contentType = res.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await res.text();
        console.error("Non-JSON response from website info endpoint:", text);
        throw new Error("Server returned non-JSON response for website info");
      }

      const data = await res.json();

      // Update brand data with fetched info
      setBrand(prev => ({
        ...prev,
        brand_name: data.brand_name || prev.brand_name,
        logo_url: data.logo_url || prev.logo_url,
        work_email: data.contact_email || prev.work_email || (user?.email || ""),
        headquarters: data.headquarters || prev.headquarters,
        application_type: data.application_type || prev.application_type,
      }));

      // Also update product data with relevant info
      setProduct(prev => ({
        ...prev,
        title: data.brand_name || prev.title,
        url: website || prev.url,
        description: data.description || prev.description,
        categories: data.category ? [data.category] : prev.categories, // Convert single category to array
        keywords: data.keywords || prev.keywords,
      }));

      return true;
    } catch (err) {
      console.error("❌ Error fetching website info:", err);
      toast.error("Could not fetch information for this website. Please fill in the details manually.");
      return false;
    } finally {
      setBrandLoading(false);
    }
  }, [user]);

  // Handle brand submission
  const handleBrandSubmit = async () => {
    setBrandLoading(true);
    try {
      const isValid = validateBrand();
      if (!isValid) {
        toast.error("Please fill in all required fields");
        return false;
      }

      // Auto-fill product data from brand information
      setProduct(prev => ({
        ...prev,
        title: brand.brand_name || prev.title,
        url: brand.website || prev.url,
        // Keep other fields as they are
      }));

      forceStepChange(2);
      return true;
    } finally {
      setBrandLoading(false);
    }
  };

  // Handle product submission
  const handleProductSubmit = async () => {
    setProductLoading(true);
    try {
      const isValid = validateProduct();
      if (!isValid) {
        toast.error("Please fill in all required fields");
        return false;
      }

      // Auto-fill offer data with default values if not already set
      if (!offer.goal || !offer.model) {
        setOffer((prev: Offer) => ({
          ...prev,
          payout_amount: "0.00", // Start with zero payout amount
          budget: "0.00",        // Start with zero budget
          goal: "signup",        // Default goal
          model: "CPA",          // Default model
          promo_applied: false   // Default promo_applied to false
        }));
      }

      forceStepChange(3);
      return true;
    } finally {
      setProductLoading(false);
    }
  };

  // Helper function to get detailed error information
  const getDetailedErrorInfo = async (response: Response) => {
    const errorText = await response.text();
    console.error(`API error. Status: ${response.status}, Response:`, errorText);

    // Try to parse the error response as JSON if possible
    let errorDetails = "";
    try {
      // Parse the error text as JSON
      const errorJson = JSON.parse(errorText);
      console.log("Parsed error JSON:", errorJson);

      // Handle FastAPI validation errors which have a specific format
      if (Array.isArray(errorJson) && errorJson.length > 0 && errorJson[0].type) {
        // This is likely a FastAPI validation error
        const validationErrors = errorJson.map(err => {
          const field = err.loc ? err.loc[err.loc.length - 1] : 'unknown';
          return `${field}: ${err.msg}`;
        }).join(', ');

        errorDetails = `: Validation errors - ${validationErrors}`;
      } else if (errorJson.detail) {
        errorDetails = `: ${errorJson.detail}`;
      } else if (errorJson.message) {
        errorDetails = `: ${errorJson.message}`;
      } else if (errorJson.error) {
        errorDetails = `: ${errorJson.error}`;
      } else {
        // If no specific error field is found, stringify the entire object
        errorDetails = `: ${JSON.stringify(errorJson)}`;
      }
    } catch (e) {
      console.error("Error parsing error response:", e);
    }

    return `${response.status} ${response.statusText}${errorDetails}`;
  };

  // Handle offer submission - submit all data up to this point
  const handleOfferSubmit = async () => {
    if (!user) return false;
    setOfferLoading(true);

    try {
      // Validate all fields before submission
      const isBrandValid = validateBrand();
      const isProductValid = validateProduct();
      const isOfferValid = validateOffer();

      if (!isBrandValid || !isProductValid || !isOfferValid) {
        toast.error("Please fill in all required fields");
        return false;
      }

      // Auto-fill tracking data if not already set
      if (!tracking.redirect_url) {
        setTracking(prev => ({
          ...prev,
          redirect_url: product.url || brand.website || "", // Use product URL or brand website as default
          method: "pixel", // Default tracking method
        }));
      }

      const token = await user.getIdToken();

      // Debug the dollarsToCents conversion
      const payoutAmountCents = dollarsToCents(offer.payout_amount);
      const budgetCents = dollarsToCents(offer.budget);

      console.log("Payout amount:", offer.payout_amount, "→", payoutAmountCents, "cents");
      console.log("Budget:", offer.budget, "→", budgetCents, "cents");

      // Prepare the payload
      const payload = {
        brand,
        product: {
          ...product,
          keywords: product.keywords,
          categories: product.categories, // Use the new categories array
        },
        offer: {
          goal: offer.goal,
          payout: {
            amount: payoutAmountCents,
            currency: "USD",
            model: offer.model || "CPA",
          },
          offer_total_budget_allocated: budgetCents,
          title: product.title,
          description: product.description,
          url: product.url,
          categories: product.categories, // Include categories in offer
          suggestion_reason: `Targeting users interested in ${offer.goal}`,
          reward_note: offer.promo_applied
            ? `$${offer.payout_amount} per ${offer.goal} (Free Plan Promo Credit - 5 max conversions)`
            : `$${offer.payout_amount} per ${offer.goal}`,
          active: true,
          promo_applied: offer.promo_applied, // Include the promo_applied flag
          offer_incentive: offer.offer_incentive, // Include offer incentive if provided
        },
        tracking: {
          method: "redirect_pixel",
          redirect_url: tracking.redirect_url || product.url || brand.website || "",
          target_urls: tracking.target_urls || [],
          notes: tracking.notes || "",
          webhook_url: tracking.webhook_url || ""
        },
        // No longer need to add the integration field - it's been removed from the API requirement
      };

      // Log the payload for debugging
      console.log("Submitting onboarding data:", JSON.stringify(payload, null, 2));

      // Submit all data to the existing setup endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/onboarding/setup`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorDetails = await getDetailedErrorInfo(response);
        throw new Error(`Failed to submit details: ${errorDetails}`);
      }

      // Check if the response is JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text();
        console.error("Non-JSON response:", text);
        throw new Error("Server returned non-JSON response");
      }

      const data = await response.json();

      // Store the IDs
      if (data.brand_id) setBrandId(data.brand_id);
      if (data.product_id) setProductId(data.product_id);
      if (data.offer_id) setOfferId(data.offer_id);

      // Set productExisted state if product already existed
      if (data.product_existed) {
        setProductExisted(true);
        toast.info(
          "Your product already exists in our system. Once onboarding is complete, you'll be able to see all analytics for this product.",
          { duration: 6000 }
        );
      }

      // Update onboarding status
      const statusResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/onboarding/status`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (statusResponse.ok) {
        // Check if the response is JSON
        const contentType = statusResponse.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const text = await statusResponse.text();
          console.error("Non-JSON response from status endpoint:", text);
          toast.error("Error fetching onboarding status. Please try again.");
        } else {
          const statusData = await statusResponse.json();
          setOnboardingStatus(statusData);
        }
      }

      toast.success("Details saved successfully!");
      forceStepChange(4);
      return true;
    } catch (err) {
      console.error("❌ Submission error:", err);

      // Show a more detailed error message
      if (err instanceof Error) {
        toast.error(`Failed to save details: ${err.message}. Please try again.`);
      } else {
        toast.error("Failed to save details. Please try again.");
      }

      // Check if this is a validation error (422)
      if (err instanceof Error && err.message.includes("422")) {
        // For validation errors, stay on the current step
        console.log("Validation error detected, staying on current step");
        return false;
      } else {
        // For other errors (network, server, etc.), we can proceed to the next step
        // This allows users to proceed with the onboarding flow even if there are backend issues
        console.log("Non-validation error, proceeding to next step");
        forceStepChange(4);
        return true;
      }
    } finally {
      setOfferLoading(false);
    }
  };

  // Handle tracking submission
  const handleTrackingSubmit = async () => {
    if (!user) {
      toast.error("You must be logged in to complete the setup");
      return false;
    }

    setLoading(true);

    try {
      // Validate tracking fields
      const isValid = validateTracking();
      if (!isValid) {
        toast.error("Please fill in all required fields");
        return false;
      }

      const token = await user.getIdToken();
      const currentBrandId = brandId || user.uid;

      // Complete the tracking setup for the existing offer
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/${offerId}/tracking/update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          product_id: productId,
          tracking: {
            method: "redirect_pixel",
            redirect_url: tracking.redirect_url,
            target_urls: tracking.target_urls || [],
            notes: tracking.notes || "",
            webhook_url: tracking.webhook_url || ""
          }
        }),
      });

      if (!response.ok) {
        const errorDetails = await getDetailedErrorInfo(response);
        throw new Error(`Failed to submit tracking details: ${errorDetails}`);
      }

      // Mark onboarding as completed
      const completeResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/${currentBrandId}/onboarding/complete`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          steps_completed: {
            brand: true,
            product: true,
            offer: true,
            tracking: true
          }
        }),
      });

      if (!completeResponse.ok) {
        const errorDetails = await getDetailedErrorInfo(completeResponse);
        throw new Error(`Failed to complete onboarding: ${errorDetails}`);
      }

      // Check if the response is JSON
      const contentType = completeResponse.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await completeResponse.text();
        console.error("Non-JSON response from completion endpoint:", text);
        throw new Error("Server returned non-JSON response for completion");
      }

      // Process the response
      const completionData = await completeResponse.json();

      // Only proceed if onboarding was actually marked as completed
      if (completionData.onboarding_status === "completed") {
        // Set onboarding as completed
        setIsSubmitted(true);

        // Clear local storage after successful submission
        if (typeof window !== 'undefined') {
          localStorage.removeItem('admesh_onboarding_data');

          // Import the constant from RedirectUrlInput
          const REDIRECT_URL_STORAGE_KEY = 'admesh_redirect_url';
          localStorage.removeItem(REDIRECT_URL_STORAGE_KEY); // Clear the saved redirect URL
        }

        // Show success message
        toast.success("Onboarding completed successfully!");

        // Redirect after showing success animation
        setTimeout(() => {
          router.push("/dashboard/brand/offers");
        }, 3000);
      } else {
        // Something went wrong with marking onboarding as completed
        toast.warning("Onboarding steps saved, but completion status could not be updated. Please try again.");
      }

      return true;
    } catch (err) {
      console.error("❌ Tracking submission error:", err);
      toast.error("Failed to save tracking details. Please try again.");
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Function to handle test conversion
  const handleTestConversion = async () => {
    await handleTestConversionUtil({
      offerId: offerId || '',
      redirectUrl: tracking.redirect_url,
      user,
      productId: productId || '',
      setIsTestingConversion
    });
  };

  // Function to check for test conversions
  const checkTestConversions = async () => {
    await checkTestConversionsUtil({
      offerId: offerId || '',
      user,
      setIsCheckingConversions,
      setTestConversionExists
    });
  };

  // Initialize component
  useEffect(() => {
    const initialize = async () => {
      if (!user) {
        setLoadingStatus(false);
        return;
      }

      try {
        setLoadingStatus(true);
        const token = await user.getIdToken();
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.useadmesh.com';

        // Check if user has completed onboarding
        const response = await fetch(
          `${apiBaseUrl}/onboarding/status`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          setOnboardingStatus(data);

          if (data.completed) {
            setIsSubmitted(true);
            return;
          }

          // If user has a brand, fetch brand info
          if (data.brand_id) {
            const brandResponse = await fetch(
              `${apiBaseUrl}/brands/${data.brand_id}`,
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (brandResponse.ok) {
              const brandInfo = await brandResponse.json();
              setBrand(brandInfo);
              setBrandId(brandInfo.id);
              setStep(2);

              // Auto-fetch website info if available
              if (brandInfo.website) {
                try {
                  await fetchWebsiteInfo(brandInfo.website);
                } catch (error) {
                  console.error("Error fetching website info:", error);
                }
              }
            }
          }
        }


        // Fetch product data to get additional details
        try {
          const productsResponse = await fetch(`${apiBaseUrl}/products/brand/all`, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (productsResponse.ok) {
            const productsData = await productsResponse.json();

            if (productsData.status === "success" && productsData.products?.length > 0) {
              const productData = productsData.products[0];

              // Update product state with fetched data
              setProduct(prev => ({
                ...prev,
                title: productData.title || prev.title,
                url: productData.url || prev.url,
                description: productData.description || prev.description,
                categories: productData.categories || (productData.category ? [productData.category] : prev.categories),
                keywords: Array.isArray(productData.keywords) ? productData.keywords : prev.keywords,
                pricing_url: productData.pricing_url || prev.pricing_url,
                audience_segment: productData.audience_segment || prev.audience_segment,
                integration_list: Array.isArray(productData.integration_list) ? productData.integration_list : prev.integration_list,
              }));

              // Set product ID if available
              if (productData.id) {
                setProductId(productData.id);
              }
              
              // Set offer ID from active_offers if available
              if (productData.active_offers?.length > 0) {
                setOfferId(productData.active_offers[0]);
              }
              
              // Set redirect URL if available
              if (productData.redirect_url) {
                setTracking(prev => ({
                  ...prev,
                  redirect_url: productData.redirect_url,
                }));

                if (typeof window !== 'undefined') {
                  localStorage.setItem('admesh_redirect_url', productData.redirect_url);
                }
              }
            }
          }
        } catch (error) {
          console.error("Error fetching product data:", error);
        }
      } catch (error) {
        console.error("Error initializing onboarding:", error);
      } finally {
        setLoadingStatus(false);
      }
    };

    initialize();
  }, [user, fetchWebsiteInfo]);

  // Clean up any subscription plan checks
  useEffect(() => {
    // This effect is intentionally left empty
    // It's here to ensure any existing subscription checks don't interfere
  }, []);

  // Save form data to local storage when step changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const formData = {
        brand,
        product,
        offer,
        tracking,
        step,
        productId,
        offerId
      };
      localStorage.setItem('admesh_onboarding_data', JSON.stringify(formData));
    }
  }, [step, brand, product, offer, tracking, productId, offerId]);

  console.log("OnboardingContainer: Render state", {
    loadingStatus,
    onboardingStatus: !!onboardingStatus,
    isSubmitted,
    step,
    user: !!user
  });

  // If loading, show loading view
  if (loadingStatus && !onboardingStatus) {
    console.log("OnboardingContainer: Showing loading view");
    return <LoadingView />;
  }

  // If submitted, show success view
  if (isSubmitted) {
    console.log("OnboardingContainer: Showing success view");
    return <SuccessView productExisted={productExisted} />;
  }

  console.log("OnboardingContainer: Rendering main onboarding UI", { step });

  return (
    <div className="w-full py-8 sm:py-12 px-4 sm:px-6 md:px-12 lg:px-24 min-h-screen flex flex-col bg-gradient-to-b from-background to-background/95 dark:from-background dark:to-background/95">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-6 sm:mb-10"
      >
        <motion.h1
          className="text-3xl sm:text-4xl font-bold mb-2 sm:mb-3 bg-gradient-to-r from-primary via-blue-500 to-primary bg-clip-text text-transparent bg-size-200 animate-gradient"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          Welcome to AdMesh
        </motion.h1>
        <motion.p
          className="text-base sm:text-lg text-muted-foreground max-w-xl mx-auto px-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          Complete these 3 simple steps to set up your brand and start connecting with your audience
        </motion.p>
      </motion.div>

      <StepIndicator step={step} />

      <div className="flex-grow flex items-start w-full">
        {/* STEP 1: BRAND */}
        {step === 1 && (
          <BrandStep
            brand={brand}
            setBrand={setBrand}
            errors={errors.brand}
            onNext={handleBrandSubmit}
            loading={brandLoading}
            userEmail={user?.email || ""}
            onFetchInfo={fetchWebsiteInfo}
          />
        )}

        {/* STEP 2: PRODUCT */}
        {step === 2 && (
          <ProductStep
            product={product}
            setProduct={setProduct}
            errors={errors.product}
            onNext={handleProductSubmit}
            onBack={() => forceStepChange(1)}
            loading={productLoading}
            productExisted={productExisted}
            plan={{ name: 'Free', keyword_limit: 10 }}
          />
        )}

        {/* STEP 3: OFFER */}
        {step === 3 && (
          <OfferStep
            offer={offer}
            setOffer={setOffer}
            errors={errors.offer}
            onNext={handleOfferSubmit}
            onBack={() => forceStepChange(2)}
            loading={offerLoading}
          />
        )}

        {/* STEP 4: TRACKING */}
        {step === 4 && (
          <TrackingStep
            tracking={tracking}
            setTracking={setTracking}
            errors={errors.tracking}
            onComplete={handleTrackingSubmit}
            onBack={() => forceStepChange(3)}
            loading={loading}
            productTitle={product.title}
            handleTestConversion={handleTestConversion}
            isTestingConversion={isTestingConversion}
            checkTestConversions={checkTestConversions}
            isCheckingConversions={isCheckingConversions}
            testConversionExists={testConversionExists}
          />
        )}
      </div>
    </div>
  );
};

export default OnboardingContainer;
