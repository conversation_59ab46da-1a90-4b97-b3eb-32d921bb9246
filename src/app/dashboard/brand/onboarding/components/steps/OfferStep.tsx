"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, ArrowRight, Info, CheckCircle2 } from "lucide-react";
import FormField from "../ui/FormField";
import FormSection from "../ui/FormSection";
import OfferStepContainer from "../ui/OfferStepContainer";
import OfferIncentiveForm from "../ui/OfferIncentiveForm";
import { Offer, OfferIncentive } from "@/types/onboarding";

interface OfferStepProps {
  offer: {
    goal: string;
    model: string;
    payout_amount: string;
    budget: string;
    promo_applied: boolean; // Flag to track if promo credit has been applied
    offer_incentive?: OfferIncentive;
  };
  setOffer: (offer: Offer) => void;
  errors: Record<string, string>;
  onNext: () => void;
  onBack: () => void;
  loading: boolean;
}

const OfferStep = ({ offer, setOffer, errors, onNext, onBack, loading }: OfferStepProps) => {
  // Function to calculate the number of conversions based on budget and payout amount
  const calculateConversions = (budget: string, payoutAmount: string): number => {
    if (!budget || !payoutAmount) return 0;

    const budgetNum = parseFloat(budget);
    const payoutNum = parseFloat(payoutAmount);

    if (isNaN(budgetNum) || isNaN(payoutNum) || payoutNum <= 0) return 0;

    return Math.floor(budgetNum / payoutNum);
  };

  return (
    <OfferStepContainer dataStep={3}>
      <FormSection
        title="Set up your offer"
        description="Define the conversion goal, payout model, and budget for your campaign."
      >
        <div className="grid gap-6 w-full">
              {/* Combined Promo Credit & Offer Summary Box */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-50/70 dark:from-blue-900/20 dark:to-blue-900/10 border border-blue-200 dark:border-blue-800/30 rounded-xl p-5 mt-3 shadow-sm">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center">
                <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 16V12M12 8H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Promo Credit
              </h4>

              {/* Promo Credit Info */}
              <div className="mb-4">
                <p className="text-sm text-blue-700 dark:text-blue-300/90">
                  Your account comes with <span className="font-semibold">$50 in promo credit</span> that can be used for up to 5 conversions.
                </p>
              </div>

              {/* Summary Section */}
              <div className="bg-white/50 dark:bg-blue-900/20 rounded-lg p-4 mb-4">
                <div className="flex items-center mb-2">
                  <Info className="h-4 w-4 mr-2 text-primary" />
                  <span className="text-sm font-medium">Campaign Details</span>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div className="bg-white/80 dark:bg-blue-900/30 rounded-md p-2">
                    <p className="text-xs text-muted-foreground">Payout Per Conversion</p>
                    <p className="text-sm font-medium">${offer.payout_amount || "0.00"}</p>
                  </div>
                  <div className="bg-white/80 dark:bg-blue-900/30 rounded-md p-2">
                    <p className="text-xs text-muted-foreground">Total Budget</p>
                    <p className="text-sm font-medium">${offer.budget || "0.00"}</p>
                  </div>
                </div>

                <div className="bg-green-100/70 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs font-medium px-3 py-2 rounded-lg flex items-center shadow-sm">
                  <CheckCircle2 className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>
                    This will allow for approximately <strong className="mx-1 text-sm">{calculateConversions(offer.budget, offer.payout_amount)}</strong> conversions
                  </span>
                </div>
              </div>

              {/* Apply Promo Credit Button */}
              {offer.promo_applied ? (
                <div className="flex items-center bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm font-medium px-4 py-2 rounded-lg">
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  Promo Credit Applied
                </div>
              ) : (
                <>
                  <Button
                    onClick={() => setOffer({
                      ...offer,
                      promo_applied: true,
                      payout_amount: "10.00", // Set default payout amount when promo is applied
                      budget: "50.00"         // Set default budget when promo is applied
                    })}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-600 border-none hover:shadow-md transition-all duration-200"
                  >
                    Apply $50 Promo Credit
                  </Button>
                  {errors.promo && (
                    <p className="text-xs text-red-500 mt-2 text-center">{errors.promo}</p>
                  )}
                </>
              )}
            </div>
          {/* Campaign Settings Card */}
          <div className="bg-white dark:bg-card/80 border border-border/30 rounded-xl p-5 shadow-sm">
            <h4 className="text-sm font-medium mb-4 flex items-center">
              <svg className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.5 4.5H13.5M12 4.5V19.5M12 19.5H8.25M12 19.5H15.75M6 7.5H18M6 10.5H18M6 13.5H18M6 16.5H18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Campaign Settings
            </h4>

            <div className="space-y-5">
              {/* Conversion Goal */}
              <FormField>
                <Label htmlFor="goal" className="text-sm font-medium">Conversion Goal</Label>
                <select
                  id="goal"
                  value={offer.goal}
                  onChange={(e) => setOffer({ ...offer, goal: e.target.value })}
                  className="w-full rounded-md border border-input bg-background p-2 focus:ring-2 focus:ring-primary/20"
                >
                  <option value="signup">Signup</option>
                  {/* <option value="purchase">Purchase</option>
                  <option value="lead">Lead</option>
                  <option value="app_install">App Install</option>
                  <option value="click">Click</option> */}
                </select>
                <p className="text-xs text-muted-foreground mt-1">What action should count as a conversion?</p>
              </FormField>

              {/* Payout Model */}
              <FormField>
                <Label htmlFor="model" className="text-sm font-medium">Payout Model</Label>
                <select
                  id="model"
                  value={offer.model}
                  onChange={(e) => setOffer({ ...offer, model: e.target.value })}
                  className="w-full rounded-md border border-input bg-background p-2 focus:ring-2 focus:ring-primary/20"
                >
                  <option value="CPA">CPA (Per Action)</option>
                  {/* <option value="CPL">CPL (Per Lead)</option>
                  <option value="CPI">CPI (Per Install)</option>
                  <option value="RevShare">Revenue Share</option> */}
                </select>
              </FormField>

              {/* Payout Amount & Budget */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField>
                  <Label htmlFor="payout_amount" className="text-sm font-medium">Payout Amount (USD)</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">$</span>
                    <Input
                      id="payout_amount"
                      type="text"
                      inputMode="decimal"
                      pattern="^\d*(\.\d{0,2})?$"
                      value={offer.payout_amount}
                      disabled={offer.promo_applied}
                      className={`pl-7 transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.payout_amount ? 'border-red-500' : ''} ${offer.promo_applied ? 'bg-muted cursor-not-allowed' : ''}`}
                    />
                    {errors.payout_amount && (
                      <p className="text-xs text-red-500 mt-1">{errors.payout_amount}</p>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {offer.promo_applied
                      ? "Set by applying promo credit"
                      : "Amount in dollars and cents"}
                  </p>
                </FormField>

                <FormField>
                  <Label htmlFor="budget" className="text-sm font-medium">Total Budget (USD)</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">$</span>
                    <Input
                      id="budget"
                      type="text"
                      inputMode="decimal"
                      pattern="^\d*(\.\d{0,2})?$"
                      value={offer.budget}
                      disabled={offer.promo_applied}
                      className={`pl-7 transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.budget ? 'border-red-500' : ''} ${offer.promo_applied ? 'bg-muted cursor-not-allowed' : ''}`}
                    />
                    {errors.budget && (
                      <p className="text-xs text-red-500 mt-1">{errors.budget}</p>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {offer.promo_applied
                      ? "Set by applying promo credit"
                      : "Amount in dollars and cents"}
                  </p>
                </FormField>
              </div>
            </div>
          </div>

          {/* Payout Amount & Budget - Hidden but kept for reference */}
          <div className="hidden grid grid-cols-1 md:grid-cols-2 gap-6">
          </div>

      

          {/* Offer Incentive Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mt-6"
          >
            <OfferIncentiveForm
              incentive={offer.offer_incentive}
              onChange={(incentive) => setOffer({ ...offer, offer_incentive: incentive })}
              disabled={loading}
            />
          </motion.div>

          {/* Navigation */}
          <div className="flex flex-col-reverse sm:flex-row justify-between gap-3 sm:gap-0 pt-6">
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={onBack}
                className="w-full sm:w-auto border-primary/20 hover:border-primary/40 dark:border-primary/30 dark:hover:border-primary/50 transition-all duration-300 rounded-full px-5"
              >
                <div className="flex items-center">
                  <ArrowLeft className="mr-2 w-4 h-4 transition-transform duration-300 group-hover:-translate-x-1" /> Back
                </div>
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
              <Button
                onClick={onNext}
                disabled={loading}
                className="px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg"
              >
                <div className="relative z-10 flex items-center">
                  Proceed to Tracking Setup
                  <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
              </Button>
            </motion.div>
          </div>
        </div>
      </FormSection>
    </OfferStepContainer>
  );
};

export default OfferStep;
