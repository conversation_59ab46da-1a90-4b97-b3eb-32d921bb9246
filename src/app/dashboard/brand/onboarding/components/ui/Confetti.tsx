"use client";

import { useEffect } from "react";

// Confetti animation component
const Confetti = () => {
  useEffect(() => {
    const createConfetti = () => {
      const confettiContainer = document.getElementById('confetti-container');
      if (!confettiContainer) return;

      for (let i = 0; i < 100; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.left = `${Math.random() * 100}%`;
        confetti.style.animationDelay = `${Math.random() * 5}s`;
        confetti.style.backgroundColor = `hsl(${Math.random() * 360}, 100%, 50%)`;
        confettiContainer.appendChild(confetti);
      }

      setTimeout(() => {
        const confettis = document.querySelectorAll('.confetti');
        confettis.forEach(c => c.remove());
      }, 2000);
    };

    createConfetti();
  }, []);

  return <div id="confetti-container" className="fixed inset-0 pointer-events-none z-50" />;
};

export default Confetti;
