"use client";

import { motion } from "framer-motion";

// Animated form field
const FormField = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
    className="space-y-2 w-full group p-3 rounded-lg hover:bg-muted/5 dark:hover:bg-muted/10 transition-all duration-300"
  >
    {children}
  </motion.div>
);

export default FormField;
