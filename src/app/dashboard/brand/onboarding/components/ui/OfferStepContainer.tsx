"use client";

import { motion } from "framer-motion";

// Animated container specifically for the OfferStep
const OfferStepContainer = ({ children, dataStep }: { children: React.ReactNode; dataStep?: number }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    transition={{ duration: 0.4, ease: "easeOut" }}
    className="w-full"
    data-step={dataStep}
  >
    {children}
  </motion.div>
);

export default OfferStepContainer;
