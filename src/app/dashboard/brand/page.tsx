"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowUpRight, MousePointerClick, DollarSign, Package, Tag } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface DashboardStats {
  totalOffers: number;
  activeOffers: number;
  totalProducts: number;
  totalClicks: number;
  totalConversions: number;
  totalSpent: number;
  walletBalance: number;
}

export default function BrandDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    totalOffers: 0,
    activeOffers: 0,
    totalProducts: 0,
    totalClicks: 0,
    totalConversions: 0,
    totalSpent: 0,
    walletBalance: 0
  });
  const [, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const token = await user.getIdToken();

        // Fetch offers data
        const offersRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Fetch wallet data
        const walletRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!offersRes.ok || !walletRes.ok) {
          throw new Error("Failed to fetch dashboard data");
        }

        const offersData = await offersRes.json();
        const walletData = await walletRes.json();

        // Calculate stats
        const activeOffers = offersData.offers.filter((offer: { active: boolean }) => offer.active).length;
        const totalProducts = new Set(offersData.offers.map((offer: { product_id: string }) => offer.product_id)).size;
        const totalClicks = offersData.totals.clicks || 0;
        const totalConversions = offersData.totals.conversions || 0;
        const totalSpent = walletData.total_spent || 0;
        const walletBalance = walletData.wallet_balance || 0;

        setStats({
          totalOffers: offersData.offers.length,
          activeOffers,
          totalProducts,
          totalClicks,
          totalConversions,
          totalSpent,
          walletBalance
        });
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  const cards = [
    {
      title: "Active Offers",
      value: stats.activeOffers,
      icon: <Tag className="h-5 w-5 text-blue-500" />,
      onClick: () => router.push("/dashboard/brand/offers")
    },
    {
      title: "Products",
      value: stats.totalProducts,
      icon: <Package className="h-5 w-5 text-purple-500" />,
      onClick: () => router.push("/dashboard/brand/products")
    },
    {
      title: "Total Clicks",
      value: stats.totalClicks,
      icon: <MousePointerClick className="h-5 w-5 text-green-500" />,
      onClick: () => router.push("/dashboard/brand/analytics")
    },
    {
      title: "Conversions",
      value: stats.totalConversions,
      icon: <ArrowUpRight className="h-5 w-5 text-orange-500" />,
      onClick: () => router.push("/dashboard/brand/analytics")
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
                  Brand Dashboard
                </h1>
                <p className="mt-2 text-sm text-gray-600">
                  Monitor your campaigns, track performance, and manage your AdMesh presence
                </p>
              </div>
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">Welcome back!</p>
                  <p className="text-xs text-gray-500">Last updated: {new Date().toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-8">
        <div className="max-w-7xl mx-auto space-y-8">

          {/* Key Metrics Grid */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Performance Overview</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {cards.map((card, index) => (
                <Card
                  key={index}
                  className="relative overflow-hidden bg-white border border-gray-200 hover:border-gray-900 hover:shadow-lg transition-all duration-200 cursor-pointer group"
                  onClick={card.onClick}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-3 rounded-xl bg-gray-100 group-hover:bg-gray-900 group-hover:text-white transition-all duration-200">
                          {card.icon}
                        </div>
                        <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">
                          {card.title}
                        </CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-3xl font-bold text-gray-900 mb-3">{card.value}</div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">View details</span>
                      <div className="h-2 w-2 bg-gray-900 rounded-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Financial Overview */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Financial Overview</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Wallet Balance */}
              <Card className="bg-white border border-gray-200 hover:border-gray-900 hover:shadow-lg transition-all duration-200 cursor-pointer group" onClick={() => router.push("/dashboard/brand/billing")}>
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 rounded-xl bg-gray-100 group-hover:bg-gray-900 transition-all duration-200">
                        <DollarSign className="h-5 w-5 text-gray-700 group-hover:text-white" />
                      </div>
                      <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">
                        Wallet Balance
                      </CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-3xl font-bold text-gray-900 mb-3">{formatCurrency(stats.walletBalance)}</div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-600">
                      Total spent: {formatCurrency(stats.totalSpent)}
                    </p>
                    <div className="h-2 w-2 bg-gray-900 rounded-full"></div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions Card */}
              <Card className="bg-white border border-gray-200 hover:border-gray-900 hover:shadow-lg transition-all duration-200 group">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-gray-100 group-hover:bg-gray-900 transition-all duration-200">
                      <ArrowUpRight className="h-5 w-5 text-gray-700 group-hover:text-white" />
                    </div>
                    <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">
                      Quick Actions
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="pt-0 space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start border-gray-200 hover:border-gray-900 hover:bg-gray-900 hover:text-white transition-all duration-200"
                    onClick={() => router.push("/dashboard/brand/offers/new")}
                  >
                    <Tag className="mr-3 h-4 w-4" />
                    <span className="font-medium">Create New Offer</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-gray-200 hover:border-gray-900 hover:bg-gray-900 hover:text-white transition-all duration-200"
                    onClick={() => router.push("/dashboard/brand/products/new")}
                  >
                    <Package className="mr-3 h-4 w-4" />
                    <span className="font-medium">Add New Product</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-gray-200 hover:border-gray-900 hover:bg-gray-900 hover:text-white transition-all duration-200"
                    onClick={() => router.push("/dashboard/brand/billing")}
                  >
                    <DollarSign className="mr-3 h-4 w-4" />
                    <span className="font-medium">Add Funds</span>
                  </Button>
                </CardContent>
              </Card>

              {/* Analytics Preview Card */}
              <Card className="bg-white border border-gray-200 hover:border-gray-900 hover:shadow-lg transition-all duration-200 cursor-pointer group" onClick={() => router.push("/dashboard/brand/analytics")}>
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 rounded-xl bg-gray-100 group-hover:bg-gray-900 transition-all duration-200">
                        <ArrowUpRight className="h-5 w-5 text-gray-700 group-hover:text-white" />
                      </div>
                      <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">
                        Analytics
                      </CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-xl font-bold text-gray-900 mb-3">View Reports</div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-600">
                      Track performance metrics
                    </p>
                    <div className="h-2 w-2 bg-gray-900 rounded-full"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* AdMesh Branding Footer */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex flex-col items-center justify-center gap-2">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>Powered by</span>
                <span className="font-semibold text-gray-900">AdMesh</span>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">BETA</span>
              </div>
              <div className="text-xs text-gray-400">
                Need help? contact us{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}
