'use client'

import { useRouter, useParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { auth } from '@/lib/firebase'
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import { ArrowLeft, Loader2, Trash2 } from 'lucide-react'

export default function EditProductPage() {
  const router = useRouter()
  const { id } = useParams() as { id: string }
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  const [form, setForm] = useState({
    title: '',
    url: '',
    description: '',
    category: '',
    keywords: '',
    is_ai_powered: false,
    has_free_tier: false,
    status: 'active',
  })

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        // Get the Firebase auth token
        const token = await auth.currentUser?.getIdToken()
        if (!token) {
          toast.error('Authentication required')
          setLoading(false)
          return
        }

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.detail || 'Failed to fetch product')
        }

        const data = await response.json()
        setForm({
          title: data.title || '',
          url: data.url || '',
          description: data.description || '',
          category: data.category || '',
          keywords: data.keywords?.join(', ') || '',
          is_ai_powered: !!data.is_ai_powered,
          has_free_tier: !!data.has_free_tier,
          status: data.status || 'active',
        })
      } catch (error) {
        console.error('Error fetching product:', error)
        toast.error(error instanceof Error ? error.message : 'Failed to fetch product')
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [id])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement
    setForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)

      // Get the Firebase auth token
      const token = await auth.currentUser?.getIdToken()
      if (!token) {
        toast.error('Authentication required')
        return
      }

      // Validate URL format
      let url = form.url
      if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`
      }

      const updateData = {
        ...form,
        url,
        keywords: form.keywords.split(',').map((k) => k.trim()).filter(k => k.length > 0),
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/${id}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Failed to update product')
      }

      toast.success('Product updated successfully')
      router.push('/dashboard/brand/products')
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update product')
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async () => {
    try {
      setDeleting(true)
      // Get the Firebase auth token
      const token = await auth.currentUser?.getIdToken()
      if (!token) {
        toast.error('Authentication required')
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Failed to delete product')
      }

      toast.success('Product deleted successfully')
      router.push('/dashboard/brand/products')
    } catch (error) {
      console.error('Error deleting product:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete product')
    } finally {
      setDeleting(false)
      setDeleteDialogOpen(false)
    }
  }

  if (loading) {
    return (
      <div className="p-6 text-center text-sm text-muted-foreground">
        <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
        Loading product...
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto py-10 px-4 space-y-6">
<div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.back()}
          className="rounded-full"
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <h2 className="text-2xl font-bold">Edit Product</h2>
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogTrigger>
            <Button variant="ghost" size="icon" className="rounded-full text-red-500 hover:text-red-700 hover:bg-red-50">
              <Trash2 className="w-4 h-4" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the product
                and remove it from our servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                {deleting ? 'Deleting...' : 'Delete'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      <div className="space-y-4">
        <div>
          <Label>Title</Label>
          <Input name="title" value={form.title} onChange={handleChange} />
        </div>

        <div>
          <Label>Product URL</Label>
          <Input name="url" value={form.url} onChange={handleChange} />
        </div>

        <div>
          <Label>Description</Label>
          <Textarea name="description" value={form.description} onChange={handleChange} />
        </div>

        <div>
          <Label>Category</Label>
          <Input name="category" value={form.category} onChange={handleChange} />
        </div>

        <div>
          <Label>Keywords (comma separated)</Label>
          <Input name="keywords" value={form.keywords} onChange={handleChange} />
        </div>

        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center space-x-2">
            <Switch name="has_free_tier" checked={form.has_free_tier} onCheckedChange={(v) =>
              setForm((prev) => ({ ...prev, has_free_tier: v }))
            } />
            <Label>Free Tier</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch name="is_ai_powered" checked={form.is_ai_powered} onCheckedChange={(v) =>
              setForm((prev) => ({ ...prev, is_ai_powered: v }))
            } />
            <Label>AI Powered</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch name="status" checked={form.status === 'active'} onCheckedChange={(v) =>
              setForm((prev) => ({ ...prev, status: v ? 'active' : 'inactive' }))
            } />
            <Label>Active</Label>
          </div>
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button disabled={saving} onClick={handleSave}>
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}
