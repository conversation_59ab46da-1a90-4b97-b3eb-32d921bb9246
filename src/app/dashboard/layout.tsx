"use client";

import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { ThemeProvider } from "next-themes";
import { usePathname } from "next/navigation";
import { CreditsProvider } from "@/contexts/credits-context";
import { BadgeProvider } from "@/contexts/badge-context";
import BadgeToastNotification from "@/components/BadgeToastNotification";
import AuthGuard from "@/components/AuthGuard";
import { useAuth } from "@/hooks/use-auth";
import UserOnboardingGuard from "@/components/UserOnboardingGuard";
import BrandOnboardingGuard from "@/components/BrandOnboardingGuard";
import AgentOnboardingGuard from "@/components/AgentOnboardingGuard";

export default function Layout({ children }: { children: React.ReactNode }) {
  // Uncomment these when the header is needed
  const pathname = usePathname();
  // const currentTitle = pathname
  //   .split("/")
  //   .filter(Boolean)
  //   .slice(-1)[0]
  //   .replace(/-/g, " ")
  //   .replace(/\b\w/g, (l) => l.toUpperCase());

  // const hideHeader = pathname === "/dashboard/user/chat";
  const { role } = useAuth();

  // Check if we're on an onboarding page - if so, skip the guards
  const isOnboardingPage = pathname?.includes("/onboarding") || false;

  // Wrap content with the appropriate onboarding guard based on user role
  const getGuardedContent = () => {
    // Skip guards for onboarding pages to prevent conflicts
    if (isOnboardingPage) {
      return children;
    }

    switch (role) {
      case "user":
        return <UserOnboardingGuard>{children}</UserOnboardingGuard>;
      case "brand":
        return <BrandOnboardingGuard>{children}</BrandOnboardingGuard>;
      case "agent":
        return <AgentOnboardingGuard>{children}</AgentOnboardingGuard>;
      default:
        return children;
    }
  };

  return (
    <AuthGuard>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <CreditsProvider>
          <BadgeProvider>
            <BadgeToastNotification />
            <SidebarProvider>
              <div className="flex min-h-screen w-full">
                {/* Sidebar */}
                <AppSidebar />

                {/* Main Area */}
                <div className="flex-1 flex flex-col">
                  {/* Top Bar */}
                  {/* {!hideHeader && (
                    <header className="h-15 px-6 flex items-center border-b border-border bg-background">
                      <h1 className="ml-4 text-lg font-semibold">{currentTitle}</h1>
                    </header>
                  )} */}

                  {/* Page Content */}
                  <main className="flex-1 overflow-y-auto ">
                    <div className="">
                      {getGuardedContent()}
                    </div>
                  </main>
                </div>
              </div>
            </SidebarProvider>
          </BadgeProvider>
        </CreditsProvider>
      </ThemeProvider>
    </AuthGuard>
  );
}
