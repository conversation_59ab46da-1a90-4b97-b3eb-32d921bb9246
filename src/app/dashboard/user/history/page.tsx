"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ExternalLink,
  CheckCheck,
  Coins,
  Clock,
  Search,
  RefreshCw,
  ChevronDown,
} from "lucide-react";
import { ReactNode, useEffect, useState, useCallback } from "react";
import { cn } from "@/lib/utils";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "@/lib/firebase";

type QueryHistory = {
  id: number;
  query: string;
  clickedProducts: {
    url: string | undefined;
    title: ReactNode;
    converted: boolean;
  }[];
  createdAt: string;
  queries?: {
    query: string;
    timestamp: string;
  }[];
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

export default function HistoryPage() {
  const [userId, setUserId] = useState<string | null>(null);
  const [history, setHistory] = useState<QueryHistory[]>([]);
  const [pages, setPages] = useState<Record<number, QueryHistory[]>>({});
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [filter, setFilter] = useState("all");
  const [runningQuery] = useState(false);
  const [openItems, setOpenItems] = useState<Record<string, boolean>>({});
  const [limit] = useState(10);
  const [offset, setOffset] = useState(0);
  const [total, setTotal] = useState(0);
  const user = auth.currentUser;
  console.log("user:", user?.getIdToken());

  const fetchHistory = useCallback(async (uid: string, newOffset: number) => {
    setLoading(true);

    if (pages[newOffset]) {
      setHistory(pages[newOffset]);
      setOffset(newOffset);
      setLoading(false);
      return;
    }

    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/chat/history?uid=${uid}&limit=${limit}&offset=${newOffset}`
      );
      const data = await res.json();

      if (Array.isArray(data?.items)) {
        setPages((prev) => ({ ...prev, [newOffset]: data.items }));
        setHistory(data.items);
        setOffset(newOffset);
        setTotal(data.total || 0);
      } else {
        console.warn("Unexpected history format:", data);
        setHistory([]);
      }
    } catch (err) {
      console.error("❌ Failed to fetch user history", err);
    } finally {
      setLoading(false);
    }
  }, [limit, pages]);

  useEffect(() => {
    const unsub = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setUserId(user.uid);
        await fetchHistory(user.uid, 0);
      } else {
        setLoading(false);
      }
    });
    return () => unsub();
  }, [fetchHistory]);

  const runQueryAgain = async (query: string) => {
    localStorage.setItem("pendingQuery", query);
    window.location.href = "/dashboard";
  };

  const filteredHistory = history
    .filter((query) => query.query.toLowerCase().includes(search.toLowerCase()))
    .filter((query) => {
      if (filter === "clicked") return query.clickedProducts.length > 0;
      if (filter === "converted")
        return query.clickedProducts.some((p) => p.converted);
      return true;
    });

  const toggleAccordion = (id: string) => {
    setOpenItems((prev) => ({ ...prev, [id]: !prev[id] }));
  };

  const renderSkeletons = () => (
    <div className="space-y-6">
      {[1, 2, 3].map((i) => (
        <div key={i} className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-5 w-64" />
              <Skeleton className="h-4 w-40" />
            </div>
            <Skeleton className="h-6 w-6 rounded-full" />
          </div>
          <div className="pl-4 space-y-2">
            <Skeleton className="h-14 w-full rounded-md" />
            <Skeleton className="h-14 w-full rounded-md" />
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          size="sm"
          onClick={() => userId && fetchHistory(userId, offset)}
          disabled={loading}
        >
          <RefreshCw className={cn("h-4 w-4 mr-1", loading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="w-full max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="🔍 Search your queries..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 bg-muted/30"
            />
          </div>
        </div>

        <div className="flex gap-2">
          {["all", "clicked", "converted"].map((type) => (
            <Button
              key={type}
              variant={filter === type ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter(type)}
            >
              {type[0].toUpperCase() + type.slice(1)}
            </Button>
          ))}
        </div>
      </div>

      {loading ? (
        renderSkeletons()
      ) : filteredHistory.length === 0 ? (
        <div className="text-muted-foreground text-center py-12 bg-muted/20 rounded-lg">
          <div className="flex justify-center mb-2">
            <Search className="h-10 w-10 text-muted-foreground/50" />
          </div>
          <p className="font-medium">No results found</p>
          <p className="text-sm mt-1">
            No queries match &quot;{search}&quot; with filter &quot;{filter}&quot;
          </p>
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {filteredHistory.map((query, idx) => {
              const clickedCount = query.clickedProducts.length;
              const convertedCount = query.clickedProducts.filter((p) => p.converted).length;
              const isOpen = openItems[`item-${idx}`] || false;

              return (
                <div key={query.id || idx} className="border border-muted rounded-md overflow-hidden">
                  <div
                    className="flex justify-between items-start p-4 cursor-pointer hover:bg-muted/30"
                    onClick={() => toggleAccordion(`item-${idx}`)}
                  >
                    <div className="flex-1">
                      <div className="text-sm font-medium flex items-center justify-between mr-6">
                        <span>{query.query}</span>
                        <Button
                          size="sm"
                          onClick={() => runQueryAgain(query.query)}
                          disabled={runningQuery}
                          className="group"
                        >
                          <RefreshCw
                            className={cn(
                              "h-4 w-4  group-hover:rotate-180 transition-transform",
                              runningQuery && "animate-spin"
                            )}
                          />
                          <span className="hidden sm:inline">Run again</span>
                        </Button>
                      </div>
                      <div className="text-xs text-muted-foreground flex flex-wrap items-center gap-2 mt-1">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" /> {formatDate(query.createdAt)}
                        </span>
                        <span className="flex items-center gap-1">
                          <CheckCheck className="h-3 w-3" /> {clickedCount} Clicked
                        </span>
                        <span className="flex items-center gap-1">
                          <Coins className="h-3 w-3" /> {convertedCount} Converted
                        </span>
                      </div>
                    </div>
                    <ChevronDown className={cn("h-5 w-5 transition-transform", isOpen && "rotate-180")} />
                  </div>

                  {isOpen && (
                    <div className="space-y-3 p-4 bg-muted/10 border-t border-muted">
                      {clickedCount > 0 ? (
                        <div className="space-y-2">
                          {query.clickedProducts.map((prod, i) => (
                            <div
                              key={i}
                              className="flex justify-between items-center bg-card p-3 rounded-lg text-sm border border-muted shadow-sm"
                            >
                              <a
                                href={prod.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="font-medium text-primary hover:underline truncate mr-2"
                              >
                                {prod.title}
                              </a>

                              <div className="flex items-center gap-2 shrink-0">
                                {prod.converted ? (
                                  <Badge
                                    variant="outline"
                                    className="text-green-600 border-green-500 flex items-center gap-1 bg-green-50"
                                  >
                                    <Coins className="w-3 h-3" /> Converted
                                  </Badge>
                                ) : (
                                  <Badge
                                    variant="outline"
                                    className="text-blue-600 border-blue-500 flex items-center gap-1 bg-blue-50"
                                  >
                                    <CheckCheck className="w-3 h-3" /> Clicked
                                  </Badge>
                                )}
                                <a
                                  href={prod.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="hover:opacity-80 p-1 rounded-md hover:bg-muted/50 transition-colors"
                                >
                                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                                </a>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground flex items-center gap-1 bg-muted/30 p-3 rounded-lg">
                          <Clock className="h-4 w-4" /> No products clicked or converted.
                        </div>
                      )}

                      {query.queries && query.queries.length > 1 && (
                        <div className="pt-2 text-sm text-muted-foreground">
                          <div className="font-medium mb-1">Follow-up queries:</div>
                          <ul className="list-disc pl-5 space-y-1">
                            {query.queries.slice(1).map((q, i) => (
                              <li key={i} className="flex justify-between items-center">
                                <span>{q.query}</span>
                                <Button
                                  size="sm"
                                  onClick={() => runQueryAgain(q.query)}
                                  disabled={runningQuery}
                                  className="group"
                                >
                                  <RefreshCw
                                    className={cn(
                                      "h-4 w-4 mr-2 group-hover:rotate-180 transition-transform",
                                      runningQuery && "animate-spin"
                                    )}
                                  />
                                  <span className="hidden sm:inline">Run again</span>
                                </Button>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <div className="flex justify-between items-center pt-6">
            <Button
              size="sm"
              variant="outline"
              onClick={() => userId && fetchHistory(userId, offset - limit)}
              disabled={offset === 0}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Showing {offset + 1} – {Math.min(offset + limit, total)} of {total}
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => userId && fetchHistory(userId, offset + limit)}
              disabled={offset + limit >= total}
            >
              Next
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
