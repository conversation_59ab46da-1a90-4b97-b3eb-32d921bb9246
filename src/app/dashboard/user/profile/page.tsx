"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Shield, Save, Mail, CheckCircle } from "lucide-react";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { sendEmailVerification } from "firebase/auth";

// Define profession options
const PROFESSION_OPTIONS = [
  "Student",
  "Engineer",
  "Founder",
  "Designer",
  "Doctor",
  "Freelancer",
  "Researcher",
  "Other"
];

// Define interest tag options
const INTEREST_OPTIONS = [
  "productivity",
  "finance",
  "ai tools",
  "writing",
  "health",
  "ecommerce",
  "marketing",
  "education",
  "design",
  "development"
];

interface UserProfile {
  name: string;
  email: string;
  agentName: string;
  keywords?: string[];
  profession?: string;
  interests?: string[];
  isPioneerEligible?: boolean;
  onboardingStatus: string;
  photoURL?: string;
}

export default function ProfilePage() {
  const { user, refreshUser } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [sendingVerification, setSendingVerification] = useState(false);
  const [formData, setFormData] = useState<Partial<UserProfile>>({});
  const [profession, setProfession] = useState("");
  const [otherProfession, setOtherProfession] = useState("");
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);

  // Function to refresh user's email verification status
  const refreshEmailVerification = useCallback(async () => {
    if (!user) return;

    try {
      // Force refresh the user's token to get the latest emailVerified status
      await user.reload();
      // Refresh the user data in the auth context
      refreshUser();
    } catch (error) {
      console.error("Error refreshing email verification status:", error);
    }
  }, [user, refreshUser]);

  // Check for email verification status when the component mounts
  useEffect(() => {
    // Check if the URL contains the email verification action code
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get('mode');
    const oobCode = urlParams.get('oobCode');

    // If the user just verified their email, refresh the verification status
    if (mode === 'verifyEmail' && oobCode && user) {
      refreshEmailVerification();
    }
  }, [user, refreshEmailVerification]);

  useEffect(() => {
    if (!user) return;

    const fetchProfile = async () => {
      setLoading(true);
      try {
        const token = await user.getIdToken();
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/me`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch profile");
        }

        const data = await response.json();
        setProfile(data);
        setFormData({
          name: data.name || user.displayName || "",
          agentName: data.agentName || "AdMesh Assistant",
        });

        // Set profession
        if (data.profession) {
          if (PROFESSION_OPTIONS.includes(data.profession)) {
            setProfession(data.profession);
          } else {
            setProfession("Other");
            setOtherProfession(data.profession);
          }
        }

        // Set interests
        if (data.interests && Array.isArray(data.interests)) {
          setSelectedInterests(data.interests);
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
        toast.error("Failed to load profile data");
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };



  // Handle interest selection
  const toggleInterest = (interest: string) => {
    if (selectedInterests.includes(interest)) {
      setSelectedInterests(selectedInterests.filter(i => i !== interest));
    } else {
      if (selectedInterests.length < 5) {
        setSelectedInterests([...selectedInterests, interest]);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setSaving(true);
    try {
      // Get profession value
      const professionValue = profession === "Other" ? otherProfession.trim() : profession;

      const updateData = {
        ...formData,
        profession: professionValue || undefined,
        interests: selectedInterests.length > 0 ? selectedInterests : undefined,
      };

      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/update-profile`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(updateData),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update profile");
      }

      toast.success("Profile updated successfully");

      // Refresh user data in auth context
      refreshUser();

      // Refresh profile data
      const updatedProfileResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (updatedProfileResponse.ok) {
        const updatedData = await updatedProfileResponse.json();
        setProfile(updatedData);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setSaving(false);
    }
  };

  const renderSkeleton = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Skeleton className="h-16 w-16 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    </div>
  );

  if (loading) {
    return <div className="p-6">{renderSkeleton()}</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">User Profile</h1>
      </div>

      {profile && (
        <>
          {/* Profile Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 bg-muted/30 rounded-lg">
            <Avatar className="h-16 w-16">
              <AvatarImage src={user?.photoURL || ""} alt={profile.name} />
              <AvatarFallback>
                {profile.name?.charAt(0) || user?.email?.charAt(0) || "U"}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="text-xl font-semibold">{profile.name || user?.email}</h2>
            </div>
          </div>

          {/* Profile Form */}
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Basic Information
                  </CardTitle>
                  <CardDescription>
                    Update your profile information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Display Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name || ""}
                      onChange={handleInputChange}
                      placeholder="Your display name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="agentName">AI Assistant Name</Label>
                    <Input
                      id="agentName"
                      name="agentName"
                      value={formData.agentName || ""}
                      onChange={handleInputChange}
                      placeholder="Name for your AI assistant"
                    />
                    <p className="text-xs text-muted-foreground">
                      This is what your AI assistant will call itself in chat
                    </p>
                  </div>


                  {/* Profession selection */}
                  <div className="space-y-3">
                    <Label>Who Are You?</Label>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                      {PROFESSION_OPTIONS.map((option) => (
                        <button
                          key={option}
                          type="button"
                          onClick={() => setProfession(option)}
                          className={`p-2 rounded-lg border text-sm font-medium transition-all ${
                            profession === option
                              ? "border-primary bg-primary/10 text-primary"
                              : "border-gray-200 hover:border-gray-300 text-gray-700"
                          }`}
                        >
                          {option}
                        </button>
                      ))}
                    </div>

                    {/* Other profession input */}
                    {profession === "Other" && (
                      <Input
                        value={otherProfession}
                        onChange={(e) => setOtherProfession(e.target.value)}
                        placeholder="Specify your profession"
                        className="mt-2"
                      />
                    )}
                  </div>

                  {/* Interest keywords */}
                  <div className="space-y-3">
                    <Label>Interest Keywords (Select up to 5)</Label>
                    <div className="flex flex-wrap gap-2">
                      {INTEREST_OPTIONS.map((interest) => (
                        <button
                          key={interest}
                          type="button"
                          onClick={() => toggleInterest(interest)}
                          disabled={selectedInterests.length >= 5 && !selectedInterests.includes(interest)}
                          className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all ${
                            selectedInterests.includes(interest)
                              ? "bg-primary text-white"
                              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                          } ${
                            selectedInterests.length >= 5 && !selectedInterests.includes(interest)
                              ? "opacity-50 cursor-not-allowed"
                              : ""
                          }`}
                        >
                          {interest}
                        </button>
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Selected: {selectedInterests.length}/5
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <div className="flex gap-2">
                      <div className="relative flex-1">
                        <Input
                          id="email"
                          name="email"
                          value={user?.email || ""}
                          disabled
                          className="bg-muted/50 pr-8"
                        />
                        {user?.emailVerified && (
                          <div className="absolute right-2 top-1/2 -translate-y-1/2 text-green-500" title="Email verified">
                            <CheckCircle className="h-4 w-4" />
                          </div>
                        )}
                      </div>
                      {!user?.emailVerified && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-10 whitespace-nowrap"
                          disabled={sendingVerification}
                          onClick={async () => {
                            try {
                              if (user) {
                                setSendingVerification(true);
                                await sendEmailVerification(user);
                                toast.success(
                                  "Verification email sent! Please check your inbox and click the link to verify your email.",
                                  {
                                    action: {
                                      label: "Refresh Status",
                                      onClick: refreshEmailVerification
                                    },
                                    duration: 10000 // Show for 10 seconds
                                  }
                                );
                              }
                            } catch (error) {
                              console.error("Error sending verification email:", error);
                              toast.error("Failed to send verification email. Please try again later.");
                            } finally {
                              setSendingVerification(false);
                            }
                          }}
                        >
                          {sendingVerification ? (
                            <>
                              <Skeleton className="h-4 w-4 mr-2 rounded-full animate-spin" />
                              Sending...
                            </>
                          ) : (
                            <>
                              <Mail className="h-4 w-4 mr-2" />
                              Verify Email
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {user?.emailVerified
                        ? "Your email is verified"
                        : "Please verify your email to unlock all features"}
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    type="submit"
                    disabled={saving}
                    className="w-full sm:w-auto"
                  >
                    {saving ? (
                      <>
                        <Skeleton className="h-4 w-4 mr-2 rounded-full animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Account Information
                  </CardTitle>
                  <CardDescription>
                    Your account details and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Account Type</Label>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="bg-primary/10">
                        User
                      </Badge>
                      {profile.isPioneerEligible && (
                        <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/20">
                          Pioneer Eligible
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Onboarding Status</Label>
                    <div>
                      <Badge variant={profile.onboardingStatus === "completed" ? "default" : "outline"}>
                        {profile.onboardingStatus === "completed" ? "Completed" : "Pending"}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Account Created</Label>
                    <div className="text-sm">
                      {user?.metadata?.creationTime
                        ? new Date(user.metadata.creationTime).toLocaleDateString()
                        : "Unknown"}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </form>
        </>
      )}
    </div>
  );
}
