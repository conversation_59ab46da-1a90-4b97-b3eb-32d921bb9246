"use client";

import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import BadgeIcon from "@/components/BadgeIcon";
import ShareBadgeModal from "@/components/ShareBadgeModal";
import ShareRankModal from "@/components/ShareRankModal";
import { Award, Sparkles, Users, Trophy, ChevronRight, Search, Share2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

// Import the BadgeData type directly from the BadgeIcon component
import type { BadgeData } from "@/components/BadgeIcon";

interface LeaderboardUser {
  id: string;
  name: string;
  avatar_url: string;
  xp: number;
  lifetime_xp: number;
  badge_count: number;
  badges: BadgeData[];
  agent_name: string;
  joined_at: string;
}

export default function LeaderboardPage() {
  const [users, setUsers] = useState<LeaderboardUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<LeaderboardUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBadge, setSelectedBadge] = useState<BadgeData | null>(null);
  const [isBadgeShareModalOpen, setIsBadgeShareModalOpen] = useState(false);
  const [isRankShareModalOpen, setIsRankShareModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<LeaderboardUser | null>(null);
  const [selectedPosition, setSelectedPosition] = useState<number>(0);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/leaderboard`);

        if (!response.ok) {
          throw new Error("Failed to fetch leaderboard data");
        }

        const data = await response.json();
        setUsers(data.users || []);
        setFilteredUsers(data.users || []);
      } catch (error) {
        console.error("Error fetching leaderboard:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
  }, []);

  // Filter users based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = users.filter(user => {
      const name = (user.agent_name || user.name).toLowerCase();
      // Check if user has any badges that match the search
      const hasBadgeMatch = user.badges.some(badge =>
        badge.metadata?.name?.toLowerCase().includes(query) ||
        badge.metadata?.description?.toLowerCase().includes(query) ||
        badge.badge_type?.toLowerCase().includes(query)
      );

      return name.includes(query) || hasBadgeMatch;
    });

    setFilteredUsers(filtered);
  }, [searchQuery, users]);

  const openShareModal = (badge: BadgeData) => {
    setSelectedBadge(badge);
    setIsBadgeShareModalOpen(true);
  };

  const openRankShareModal = (user: LeaderboardUser, position: number) => {
    setSelectedUser(user);
    setSelectedPosition(position);
    setIsRankShareModalOpen(true);
  };

  if (loading) {
    return (
      <div className="container max-w-6xl mx-auto py-10 px-4">
        <div className="mb-10 text-center">
          <Skeleton className="h-14 w-72 mx-auto mb-4" />
          <Skeleton className="h-5 w-80 mx-auto" />
        </div>

        <div className="mb-8">
          <Skeleton className="h-12 w-full rounded-full" />
        </div>

        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex gap-4 items-center p-6 bg-card rounded-xl border border-card">
              <Skeleton className="h-20 w-20 rounded-full" />
              <div className="flex-1">
                <Skeleton className="h-6 w-48 mb-3" />
                <div className="flex gap-3">
                  <Skeleton className="h-8 w-20 rounded-full" />
                  <Skeleton className="h-8 w-24 rounded-full" />
                </div>
              </div>
              <div className="flex gap-1">
                <Skeleton className="h-10 w-10 rounded-full" />
                <Skeleton className="h-10 w-10 rounded-full" />
                <Skeleton className="h-10 w-10 rounded-full" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/95">
      <div className="container max-w-5xl mx-auto py-16 px-4 mt-8">
        {selectedBadge && (
          <ShareBadgeModal
            badge={selectedBadge}
            isOpen={isBadgeShareModalOpen}
            onClose={() => setIsBadgeShareModalOpen(false)}
          />
        )}

        {selectedUser && (
          <ShareRankModal
            position={selectedPosition}
            xp={selectedUser.lifetime_xp || 0}
            badgeCount={selectedUser.badge_count}
            isOpen={isRankShareModalOpen}
            onClose={() => setIsRankShareModalOpen(false)}
          />
        )}

        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold mb-4">
            <span className="text-primary">AdMesh</span> Leaderboard
          </h1>
          <p className="text-xl text-muted-foreground mx-auto max-w-2xl mb-8">
            Discover and celebrate the top performing agents in our network
          </p>

          <div className="max-w-md mx-auto relative">
            <Input
              type="text"
              placeholder="Search by name or badge..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-6 rounded-full"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          </div>
        </div>

        <Tabs defaultValue="xp" className="w-full">
          <div className="relative mb-12">
            <TabsList className="w-full flex justify-center gap-2 p-1 bg-transparent">
              <TabsTrigger
                value="xp"
                className="flex-1 max-w-xs rounded-full border border-yellow-500/30 data-[state=active]:bg-yellow-500 data-[state=active]:text-white data-[state=active]:shadow-md py-3 px-6"
              >
                <div className="flex items-center justify-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  <span className="font-medium">XP Leaders</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="badges"
                className="flex-1 max-w-xs rounded-full border border-purple-500/30 data-[state=active]:bg-purple-500 data-[state=active]:text-white data-[state=active]:shadow-md py-3 px-6"
              >
                <div className="flex items-center justify-center gap-2">
                  <Award className="h-5 w-5" />
                  <span className="font-medium">Badge Champions</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="newest"
                className="flex-1 max-w-xs rounded-full border border-blue-500/30 data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md py-3 px-6"
              >
                <div className="flex items-center justify-center gap-2">
                  <Users className="h-5 w-5" />
                  <span className="font-medium">Newest Agents</span>
                </div>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="xp">
            <div className="grid gap-6">
              {/* All Users Section */}
              <div className="space-y-2 mt-4">
                {filteredUsers
                  .sort((a, b) => b.lifetime_xp - a.lifetime_xp)
                  .map((user, index) => {
                    const position = index + 1;
                    let medalColor = "";

                    if (position === 1) {
                      medalColor = "bg-yellow-500";
                    } else if (position === 2) {
                      medalColor = "bg-gray-400";
                    } else if (position === 3) {
                      medalColor = "bg-amber-600";
                    } else {
                      medalColor = "bg-foreground/10";
                    }

                    return (
                      <div
                        key={user.id}
                        className="group flex items-center gap-3 p-3 rounded-lg bg-card hover:bg-card/80 border border-border/50 transition-all hover:shadow-sm cursor-pointer"
                      >
                        <div className={cn("w-6 h-6 rounded-full flex items-center justify-center font-medium text-xs text-white", medalColor)}>
                          {position}
                        </div>

                        <Avatar className="h-12 w-12">
                          <AvatarImage src={user.avatar_url} alt={user.agent_name || user.name} />
                          <AvatarFallback>
                            {(() => {
                              const name = user.agent_name || user.name;
                              const firstLetter = name.charAt(0);
                              const lastLetter = name.charAt(name.length - 1);
                              return `${firstLetter}${lastLetter}`.toUpperCase();
                            })()}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{user.agent_name || user.name}</h3>
                            {user.badges.some(badge => badge.badge_type === "agent_pioneer") && (
                              <span className="text-xs px-1 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full flex items-center gap-0.5">
                                <Sparkles className="h-2 w-2" />
                                Pioneer
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm mt-1">
                            <div className="flex items-center text-amber-600">
                              <Trophy className="h-4 w-4 mr-1" />
                              {user.lifetime_xp !== undefined ? user.lifetime_xp.toLocaleString() : "0"}
                            </div>
                            <div className="flex items-center text-purple-600">
                              <Award className="h-4 w-4 mr-1" />
                              {user.badge_count}
                            </div>
                          </div>

                          <div className="flex items-center mt-2">
                            <div className="flex -space-x-1">
                              {user.badges.slice(0, 3).map((badge) => (
                                <div
                                  key={badge.badge_id}
                                  className="relative cursor-pointer hover:z-10 transition-all hover:scale-110"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openShareModal(badge);
                                  }}
                                >
                                  <BadgeIcon badge={badge} size="sm" showTooltip={true} className="border border-white dark:border-gray-800" />
                                </div>
                              ))}
                              {user.badges.length > 3 && (
                                <div className="relative z-0 flex items-center justify-center w-6 h-6 text-xs font-medium text-white bg-slate-600 rounded-full border border-white dark:border-gray-800">
                                  +{user.badges.length - 3}
                                </div>
                              )}
                            </div>

                            <Button
                              variant="outline"
                              size="sm"
                              className="ml-auto h-7 text-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                openRankShareModal(user, position);
                              }}
                            >
                              <Share2 className="h-3 w-3 mr-1" />
                              Share Rank
                            </Button>
                          </div>
                        </div>

                        <ChevronRight className="h-5 w-5 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity ml-2" />
                      </div>
                    );
                  })}
              </div>


            </div>
          </TabsContent>

          <TabsContent value="badges">
            <div className="grid gap-6">
              {/* All Users Section */}
              <div className="space-y-2 mt-4">
                {filteredUsers
                  .sort((a, b) => b.badge_count - a.badge_count)
                  .map((user, index) => {
                    const position = index + 1;
                    let medalColor = "";

                    if (position === 1) {
                      medalColor = "bg-purple-500";
                    } else if (position === 2) {
                      medalColor = "bg-gray-400";
                    } else if (position === 3) {
                      medalColor = "bg-indigo-600";
                    } else {
                      medalColor = "bg-foreground/10";
                    }

                    return (
                      <div
                        key={user.id}
                        className="group flex items-center gap-3 p-3 rounded-lg bg-card hover:bg-card/80 border border-border/50 transition-all hover:shadow-sm cursor-pointer"
                      >
                        <div className={cn("w-6 h-6 rounded-full flex items-center justify-center font-medium text-xs text-white", medalColor)}>
                          {position}
                        </div>

                        <Avatar className="h-12 w-12">
                          <AvatarImage src={user.avatar_url} alt={user.agent_name || user.name} />
                          <AvatarFallback>
                            {(() => {
                              const name = user.agent_name || user.name;
                              const firstLetter = name.charAt(0);
                              const lastLetter = name.charAt(name.length - 1);
                              return `${firstLetter}${lastLetter}`.toUpperCase();
                            })()}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{user.agent_name || user.name}</h3>
                            {user.badges.some(badge => badge.badge_type === "agent_pioneer") && (
                              <span className="text-xs px-1 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full flex items-center gap-0.5">
                                <Sparkles className="h-2 w-2" />
                                Pioneer
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm mt-1">
                            <div className="flex items-center text-purple-600">
                              <Award className="h-4 w-4 mr-1" />
                              {user.badge_count}
                            </div>
                            <div className="flex items-center text-amber-600">
                              <Trophy className="h-4 w-4 mr-1" />
                              {user.lifetime_xp !== undefined ? user.lifetime_xp.toLocaleString() : "0"}
                            </div>
                          </div>

                          <div className="flex items-center mt-2">
                            <div className="flex -space-x-1">
                              {user.badges.slice(0, 3).map((badge) => (
                                <div
                                  key={badge.badge_id}
                                  className="relative cursor-pointer hover:z-10 transition-all hover:scale-110"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openShareModal(badge);
                                  }}
                                >
                                  <BadgeIcon badge={badge} size="sm" showTooltip={true} className="border border-white dark:border-gray-800" />
                                </div>
                              ))}
                              {user.badges.length > 3 && (
                                <div className="relative z-0 flex items-center justify-center w-6 h-6 text-xs font-medium text-white bg-slate-600 rounded-full border border-white dark:border-gray-800">
                                  +{user.badges.length - 3}
                                </div>
                              )}
                            </div>

                            <Button
                              variant="outline"
                              size="sm"
                              className="ml-auto h-7 text-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                openRankShareModal(user, position);
                              }}
                            >
                              <Share2 className="h-3 w-3 mr-1" />
                              Share Rank
                            </Button>
                          </div>
                        </div>

                        <ChevronRight className="h-5 w-5 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity ml-2" />
                      </div>
                    );
                  })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="newest">
            <div className="grid gap-6">
              {/* All Users Section */}
              <div className="space-y-2 mt-4">
                {filteredUsers
                  .sort((a, b) => new Date(b.joined_at).getTime() - new Date(a.joined_at).getTime())
                  .map((user, index) => {
                    const position = index + 1;

                    return (
                      <div
                        key={user.id}
                        className="group flex items-center gap-3 p-3 rounded-lg bg-card hover:bg-card/80 border border-border/50 transition-all hover:shadow-sm cursor-pointer"
                      >
                        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center font-medium text-xs text-white">
                          {position}
                        </div>

                        <Avatar className="h-12 w-12">
                          <AvatarImage src={user.avatar_url} alt={user.agent_name || user.name} />
                          <AvatarFallback>
                            {(() => {
                              const name = user.agent_name || user.name;
                              const firstLetter = name.charAt(0);
                              const lastLetter = name.charAt(name.length - 1);
                              return `${firstLetter}${lastLetter}`.toUpperCase();
                            })()}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{user.agent_name || user.name}</h3>
                            {user.badges.some(badge => badge.badge_type === "agent_pioneer") && (
                              <span className="text-xs px-1 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full flex items-center gap-0.5">
                                <Sparkles className="h-2 w-2" />
                                Pioneer
                              </span>
                            )}
                            <span className="text-xs px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full">
                              New
                            </span>
                          </div>

                          <div className="flex items-center gap-4 text-sm mt-1">
                            <div className="text-muted-foreground text-xs">
                              Joined {new Date(user.joined_at).toLocaleDateString(undefined, {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </div>
                          </div>

                          <div className="flex items-center gap-4 text-sm mt-1">
                            <div className="flex items-center text-amber-600">
                              <Trophy className="h-4 w-4 mr-1" />
                              {user.lifetime_xp !== undefined ? user.lifetime_xp.toLocaleString() : "0"}
                            </div>
                            <div className="flex items-center text-purple-600">
                              <Award className="h-4 w-4 mr-1" />
                              {user.badge_count}
                            </div>
                          </div>

                          <div className="flex items-center mt-2">
                            <div className="flex -space-x-1">
                              {user.badges.slice(0, 3).map((badge) => (
                                <div
                                  key={badge.badge_id}
                                  className="relative cursor-pointer hover:z-10 transition-all hover:scale-110"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openShareModal(badge);
                                  }}
                                >
                                  <BadgeIcon badge={badge} size="sm" showTooltip={true} className="border border-white dark:border-gray-800" />
                                </div>
                              ))}
                              {user.badges.length > 3 && (
                                <div className="relative z-0 flex items-center justify-center w-6 h-6 text-xs font-medium text-white bg-slate-600 rounded-full border border-white dark:border-gray-800">
                                  +{user.badges.length - 3}
                                </div>
                              )}
                              {user.badges.length === 0 && (
                                <div className="text-xs text-muted-foreground">No badges yet</div>
                              )}
                            </div>

                            <Button
                              variant="outline"
                              size="sm"
                              className="ml-auto h-7 text-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Find position in the overall leaderboard
                                const position = filteredUsers
                                  .sort((a, b) => b.lifetime_xp - a.lifetime_xp)
                                  .findIndex(u => u.id === user.id) + 1;
                                openRankShareModal(user, position);
                              }}
                            >
                              <Share2 className="h-3 w-3 mr-1" />
                              Share Rank
                            </Button>
                          </div>
                        </div>

                        <ChevronRight className="h-5 w-5 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity ml-2" />
                      </div>
                    );
                  })}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}