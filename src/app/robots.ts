import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/dashboard/admin/',
        '/dashboard/user/',
        '/dashboard/brand/',
        '/dashboard/agent/',
        '/api/',
        '/test-*',
        '/seo-test',
        '/*?ref=*',
        '/*?utm_*',
        '/join-now',
        '/join',
        '/signup',
        '/register',
      ],
    },
    sitemap: 'https://useadmesh.com/sitemap.xml',
  };
}
