"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function TestOGStaticPage() {
  const [path, setPath] = useState("/");
  const [routeName, setRouteName] = useState("root");
  const [ogImageUrl, setOgImageUrl] = useState(`/og-images/${routeName}-og.png`);
  const [origin, setOrigin] = useState("");

  // Set origin on client-side only
  useEffect(() => {
    setOrigin(window.location.origin);
  }, []);

  // Update route name when path changes
  useEffect(() => {
    const newRouteName = path === "/" ? "root" : path.replace(/^\//, '').split('/')[0];
    setRouteName(newRouteName);
    setOgImageUrl(`/og-images/${newRouteName}-og.png`);
  }, [path]);

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Test Static OG Images</h1>
      <p className="mb-8 text-gray-600 dark:text-gray-300">
        This page allows you to test the static OG images for different routes.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-medium mb-4">Static OG Image Settings</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="path">Path</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="path"
                  value={path}
                  onChange={(e) => setPath(e.target.value)}
                  placeholder="/brand"
                />
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Enter a path like /, /brand, /agent, etc.
              </p>
            </div>

            <div>
              <p className="text-sm font-medium">Route Name: {routeName}</p>
              <p className="text-sm text-gray-500">
                This is the name used for the OG image file.
              </p>
            </div>

            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">Generate OG Images</h3>
              <p className="text-sm text-gray-500 mb-2">
                Run the following command to generate static OG images:
              </p>
              <div className="p-2 bg-gray-100 rounded-md dark:bg-gray-800 overflow-x-auto">
                <code className="text-sm">npm run generate-og</code>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-medium mb-4">Preview</h2>
          <div className="border border-gray-300 rounded-md overflow-hidden dark:border-gray-700">
            <div className="relative w-full" style={{ height: '315px' }}>
              <Image
                src={ogImageUrl}
                alt="OG Image Preview"
                fill
                style={{ objectFit: 'contain' }}
              />
            </div>
          </div>

          <div className="mt-4">
            <h3 className="text-lg font-medium mb-2">URL</h3>
            <div className="p-2 bg-gray-100 rounded-md dark:bg-gray-800 overflow-x-auto">
              <code className="text-sm break-all">{`${origin}${ogImageUrl}`}</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
