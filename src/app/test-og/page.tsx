"use client";

import { useState, useEffect } from "react";
import Image from "next/image";

export default function TestOGPage() {
  const [title, setTitle] = useState("AdMesh – Promote your products inside AI");
  const [description, setDescription] = useState(
    "Explore tools, earn rewards, and power the future of monetized intent with AdMesh."
  );
  const [type, setType] = useState("default");
  const [mode, setMode] = useState("dark");
  const [ogImageUrl, setOgImageUrl] = useState(
    `/api/og?title=${encodeURIComponent(title)}&description=${encodeURIComponent(description)}&type=${type}&mode=${mode}`
  );
  const [origin, setOrigin] = useState("");

  // Set origin on client-side only
  useEffect(() => {
    setOrigin(window.location.origin);
  }, []);

  const updateOgImage = () => {
    setOgImageUrl(
      `/api/og?title=${encodeURIComponent(title)}&description=${encodeURIComponent(description)}&type=${type}&mode=${mode}`
    );
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Test OG Image Generator</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Title</label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Type</label>
            <select
              value={type}
              onChange={(e) => setType(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-700"
            >
              <option value="default">Default</option>
              <option value="brand">Brand</option>
              <option value="agent">Agent</option>
              <option value="user">User</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Mode</label>
            <select
              value={mode}
              onChange={(e) => setMode(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-700"
            >
              <option value="dark">Dark</option>
              <option value="light">Light</option>
            </select>
          </div>

          <button
            onClick={updateOgImage}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Generate OG Image
          </button>
        </div>

        <div>
          <h2 className="text-xl font-medium mb-4">Preview</h2>
          <div className="border border-gray-300 rounded-md overflow-hidden dark:border-gray-700">
            <div className="relative w-full" style={{ height: '315px' }}>
              <Image
                src={ogImageUrl}
                alt="OG Image Preview"
                fill
                style={{ objectFit: 'contain' }}
              />
            </div>
          </div>

          <div className="mt-4">
            <h3 className="text-lg font-medium mb-2">URL</h3>
            <div className="p-2 bg-gray-100 rounded-md dark:bg-gray-800 overflow-x-auto">
              <code className="text-sm break-all">{`${origin}${ogImageUrl}`}</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
