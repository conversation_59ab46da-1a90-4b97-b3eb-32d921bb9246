"use client";

import { useRouter } from "next/navigation";
import UserContent from "@/components/UserContent";
import FloatingRoleSwitcher from "@/components/FloatingRoleSwitcher";
import Footer from "@/components/Footer";

type Role = "user" | "brand" | "agent" | null;

export default function UsersPage() {
  const router = useRouter();

  const handleRoleChange = (role: Role) => {
    if (role === "brand") {
      router.push("/");
    } else if (role === "agent") {
      router.push("/agents");
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <UserContent onRoleChange={handleRoleChange} />

      {/* Floating Role Switcher */}
      <FloatingRoleSwitcher
        currentRole="user"
        onRoleChange={handleRoleChange}
      />

      <Footer />
    </div>
  );
}
