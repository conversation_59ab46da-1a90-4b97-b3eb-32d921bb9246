"use client";

import { Globe, <PERSON><PERSON>he<PERSON>, BarChart4 } from "lucide-react";
import { motion } from "framer-motion";

const cardVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4, ease: "easeOut" },
  },
  hover: {
    scale: 1.03,
    boxShadow: "0px 8px 28px rgba(0, 0, 0, 0.08)",
    transition: { duration: 0.2 },
  },
};

export default function AdMeshVision() {
  return (
    <section
      id="what-is-admesh"
      className="w-full bg-gradient-to-b from-white via-blue-50 to-white py-32 px-6"
    >
      <div className="mx-auto max-w-5xl text-center">
        {/* Section Title */}
        <motion.h2
          className="text-4xl sm:text-5xl font-extrabold text-gray-900 mb-6"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          viewport={{ once: true }}
        >
          AdMesh Vision
        </motion.h2>

        {/* Subtitle */}
        <motion.p
          className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto mb-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          viewport={{ once: true }}
        >
          The future of monetization is agent-native, transparent, and aligned with real user intent.
          AdMesh is building the infrastructure to power this shift — from noisy ads to verified outcomes.
        </motion.p>

        {/* Vision Cards with animation */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-3 gap-10 mt-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={{
            visible: {
              transition: { staggerChildren: 0.15 },
            },
          }}
        >
          {[
            {
              title: "Open & Interoperable",
              icon: <Globe className="h-8 w-8 text-indigo-600" />,
              description:
                "AdMesh is built as a protocol — not a platform. Any agent, tool, or brand can integrate and benefit.",
            },
            {
              title: "Privacy-Respecting by Design",
              icon: <ShieldCheck className="h-8 w-8 text-indigo-600" />,
              description:
                "We avoid invasive tracking. Offers are matched to real-time intent — not hidden data profiles.",
            },
            {
              title: "Aligned Incentives",
              icon: <BarChart4 className="h-8 w-8 text-indigo-600" />,
              description:
                "Users, agents, and brands all earn based on verified outcomes — clicks, installs, purchases.",
            },
          ].map((item, idx) => (
            <motion.div
              key={idx}
              className="bg-white border border-border rounded-xl p-6 cursor-default"
              variants={cardVariants}
              whileHover="hover"
            >
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="bg-indigo-100 rounded-full p-3">{item.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900">{item.title}</h3>
                <p className="text-sm text-muted-foreground">{item.description}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
