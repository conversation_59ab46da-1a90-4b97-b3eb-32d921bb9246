"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

interface AuthGuardProps {
  children: React.ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Wait for auth to finish loading
    if (!isLoading) {
      // If no user is logged in, redirect to home page
      if (!user) {
        router.replace("/");
      }
      setIsChecking(false);
    }
  }, [user, isLoading, router]);

  // Show loading spinner while checking authentication
  if (isLoading || isChecking) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Verifying user...</p>
      </div>
    );
  }

  // If user is not authenticated, return null (redirect happens in useEffect)
  if (!user) {
    return null;
  }

  // If user is authenticated, render children
  return <>{children}</>;
}
