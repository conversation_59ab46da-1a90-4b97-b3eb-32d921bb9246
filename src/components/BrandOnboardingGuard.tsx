"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

interface BrandOnboardingGuardProps {
  children: React.ReactNode;
}

export default function BrandOnboardingGuard({ children }: BrandOnboardingGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, role, onboardingStatus, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isOnboardingComplete, setIsOnboardingComplete] = useState<boolean | null>(null);

  useEffect(() => {
    // Skip the guard if not a brand user
    if (role !== "brand") {
      console.log("BrandOnboardingGuard: Not a brand user, skipping guard");
      setIsLoading(false);
      return;
    }

    // Skip if already on the onboarding page to prevent infinite redirects
    if (pathname === "/dashboard/brand/onboarding") {
      console.log("BrandOnboardingGuard: Already on onboarding page, skipping guard");
      setIsLoading(false);
      return;
    }

    const checkOnboardingStatus = async () => {
      // Wait for auth to finish loading
      if (authLoading) {
        console.log("BrandOnboardingGuard: Auth still loading, waiting...");
        return;
      }

      console.log("BrandOnboardingGuard: Checking onboarding status", { onboardingStatus, user: !!user });

      // First check if we already have the status from Firebase claims
      if (onboardingStatus !== null) {
        const isCompleted = onboardingStatus === "completed";
        console.log("BrandOnboardingGuard: Using onboarding status from claims", { onboardingStatus, isCompleted });
        setIsOnboardingComplete(isCompleted);
        setIsLoading(false);

        // If onboarding is not completed, redirect to onboarding page
        if (!isCompleted) {
          console.log("BrandOnboardingGuard: Redirecting to onboarding page");
          router.replace("/dashboard/brand/onboarding");
        }
        return;
      }

      // If we don't have the status from claims, check with the API
      if (user) {
        try {
          console.log("BrandOnboardingGuard: Fetching onboarding status from API");
          const token = await user.getIdToken();
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/onboarding/status`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            const isCompleted = data.onboarding_status === "completed";
            console.log("BrandOnboardingGuard: API response", { data, isCompleted });
            setIsOnboardingComplete(isCompleted);

            // If onboarding is not completed, redirect to onboarding page
            if (!isCompleted) {
              console.log("BrandOnboardingGuard: Redirecting to onboarding page based on API response");
              router.replace("/dashboard/brand/onboarding");
            }
          } else {
            console.error("BrandOnboardingGuard: API call failed", response.status, response.statusText);
            // If API call fails, we'll show the page anyway
            setIsOnboardingComplete(true);
          }
        } catch (error) {
          console.error("BrandOnboardingGuard: Error checking onboarding status:", error);
          // If API call fails, we'll show the page anyway
          setIsOnboardingComplete(true);
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log("BrandOnboardingGuard: No user found");
        setIsLoading(false);
      }
    };

    checkOnboardingStatus();
  }, [user, role, router, onboardingStatus, authLoading, pathname]);

  // Show loading spinner while checking onboarding status
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Checking onboarding status...</p>
      </div>
    );
  }

  // If not a brand user or onboarding is complete, render the children
  if (role !== "brand" || isOnboardingComplete) {
    return <>{children}</>;
  }

  // This should not be visible as we redirect in the useEffect
  return null;
}
