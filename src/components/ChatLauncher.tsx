// components/ChatLauncher.tsx
"use client";

import { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Footer,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Bot,
  User,
  Send,
  Tag,
  Gift,
  Sparkles,
  ChevronRight,
  Star,
  ArrowRight,
  MessageSquare,
  RefreshCw,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { auth } from "@/lib/firebase";
import { onAuthStateChanged, User as FirebaseUser } from "firebase/auth";
import AuthModal from "./AuthModal";
import { cn } from "@/lib/utils";

const models = [
  {
    id: "mistralai/mistral-7b-instruct",
    name: "Mistral",
    color: "bg-blue-600",
  },
];

interface Message {
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  recommendations?: Recommendation[];
  followupQuestions?: string[];
}

interface Recommendation {
  admesh_link: string;
  offer_id?: string;
  title: string;
  reason: string;
  url: string;
  category: string;
  tags: string[];
  has_free_tier: boolean;
  is_ai_powered: boolean;
  is_open_source: boolean;
  audience: string;
}

interface ChatProps {
  open?: boolean;
  onClose?: () => void;
  query?: string;
}

export default function ChatLauncher({
  open = true,
  onClose,
  query,
}: ChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState(query || "");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState(models[0].id);
  const [hasFollowedUp, setHasFollowedUp] = useState(false);
  const [initialQuery, setInitialQuery] = useState<string | null>(null);
  const [initialSummary, setInitialSummary] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [authOpen, setAuthOpen] = useState(false);
  const [viewportHeight, setViewportHeight] = useState("100vh"); // Added for mobile optimization
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const selectedModelMeta = models.find((m) => m.id === selectedModel)!;
  const agentId =
    process.env.NEXT_PUBLIC_AGENT_ID || "ZAinjf9SuPYCk7u8r0ZEeKY2fV42";
  const userId = user?.uid || "anonymous";
  const [suggestedQueries, setSuggestedQueries] = useState<string[]>([]);

  useEffect(() => {
    const updateHeight = () => {
      const vh = window.innerHeight * 0.01;
      setViewportHeight(`${vh * 100}px`);
    };
    updateHeight();
    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []); // Added effect for dynamic viewport height

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    if (open && inputRef.current && messages.length > 0) {
      inputRef.current.focus();
    }
  }, [open, messages.length]);

  useEffect(() => {
    fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/openrouter/suggest`)
      .then((res) => res.json())
      .then((data) => {
        if (Array.isArray(data.suggestions)) {
          setSuggestedQueries(data.suggestions);
        }
      })
      .catch(() => {
        setSuggestedQueries([
          "Best free tools for cold email campaigns",
          "Affordable CRM for solo founders",
          "Tools with AI-powered analytics",
          "Alternatives to Mailchimp",
        ]);
      });
  }, []);

  useEffect(() => {
    const unsub = onAuthStateChanged(auth, (u) => setUser(u));
    return () => unsub();
  }, []);

  const handleSendMessage = async (overrideQuery?: string) => {
    if (!user) {
      setAuthOpen(true);
      return;
    }

    const queryToSend = (overrideQuery || inputMessage).trim();
    if (!queryToSend || hasFollowedUp) return;

    const userMessage: Message = {
      role: "user",
      content: queryToSend,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    const payload: Record<string, unknown> = {
      query: queryToSend,
      model: selectedModel,
      agent_id: agentId,
      user_id: user.uid,
      ...(sessionId && { session_id: sessionId }),
    };

    if (initialQuery && initialSummary) {
      payload.previous_query = initialQuery;
      payload.summary = initialSummary;
    }

    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/openrouter/recommend`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        }
      );

      const json = await res.json();

      const botMessage: Message = {
        role: "assistant",
        content: json?.response?.summary || "Here's what I found.",
        timestamp: new Date(),
        recommendations: json?.response?.recommendations || [],
        followupQuestions: json?.response?.followup_questions || [],
      };

      setMessages((prev) => [...prev, botMessage]);

      if (json?.session_id) setSessionId(json.session_id);

      if (!initialQuery) {
        setInitialQuery(queryToSend);
        setInitialSummary(json?.response?.summary || "");
      }

      if (json?.end_of_session) {
        setHasFollowedUp(true);
      }
    } catch (err) {
      console.error("❌ Error hitting /recommend:", err);
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "⚠️ Could not reach the recommendation engine.",
          timestamp: new Date(),
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTrackClick = async (productId: string, offerId?: string, isTest: boolean = false) => {
    if (!offerId) return;
    await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/click`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        product_id: productId,
        offer_id: offerId,
        agent_id: agentId,
        user_id: userId,
        session_id: sessionId,
        timestamp: new Date().toISOString(),
        is_test: isTest
      }),
    });
  };

  const handleLogConversion = async (productId: string, offerId?: string) => {
    if (!offerId) return;
    await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/conversion/log`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        product_id: productId,
        offer_id: offerId,
        agent_id: agentId,
        user_id: userId,
        session_id: sessionId,
        event_type: "conversion",
        conversion_value: 10.0,
        currency: "USD",
      }),
    });
  };

  const resetConversation = () => {
    setMessages([]);
    setInputMessage("");
    setSessionId(null);
    setInitialQuery(null);
    setInitialSummary(null);
    setHasFollowedUp(false);
  };

  const getGradient = (i: number) => {
    const gradients = [
      "from-blue-50 to-violet-50 border-blue-200",
      "from-emerald-50 to-teal-50 border-emerald-200",
      "from-amber-50 to-orange-50 border-amber-200",
      "from-rose-50 to-pink-50 border-rose-200",
    ];
    return gradients[i % gradients.length];
  };

  const renderBotMessage = (msg: Message, idx: number) => (
    <motion.div
      key={`bot-${idx}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
      className="mb-4 flex justify-start"
    >
      <div className="flex gap-2 max-w-[90%]">
        <div className="mt-1 flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-tr from-indigo-600 to-violet-600 flex items-center justify-center text-white shadow-md">
          <Bot className="h-4 w-4" />
        </div>
        <div className="space-y-2">
          <div className="px-4 py-3 rounded-2xl bg-white dark:bg-slate-800 shadow-sm border border-gray-100 dark:border-slate-700 text-gray-800 dark:text-gray-200">
            <div className="flex items-center gap-2 mb-1 text-xs text-gray-500 dark:text-gray-400">
              <span className="font-medium text-indigo-600 dark:text-indigo-400">
                {selectedModelMeta.name}
              </span>
              <span className="opacity-70">
                {msg.timestamp.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </span>
            </div>
            <div className="text-sm whitespace-pre-wrap leading-relaxed">
              {msg.content}
            </div>
          </div>

          {(msg.recommendations ?? []).length > 0 && (
            <div className="space-y-4 w-full">
              {(msg.recommendations ?? []).map((rec, i) => {
                const productId = rec.title
                  .toLowerCase()
                  .replace(/\s+/g, "-");
                return (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * (i + 1) }}
                    className={`p-4 rounded-xl border bg-gradient-to-br ${getGradient(
                      i
                    )} dark:bg-slate-800 dark:border-slate-700 shadow-sm hover:shadow-md transition-shadow`}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-semibold text-gray-900 dark:text-white flex items-center">
                        {rec.is_ai_powered && (
                          <span className="mr-2 text-xs px-2 py-0.5 rounded-full bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300">
                            AI
                          </span>
                        )}
                        {rec.title}
                      </h4>
                      <a
                        href={rec.admesh_link || rec.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={() => {
                          handleTrackClick(productId, rec.offer_id);
                          handleLogConversion(productId, rec.offer_id);
                        }}
                        className="text-xs px-3 py-1 rounded-full bg-indigo-600 text-white hover:bg-indigo-700 transition-colors flex items-center shadow-sm"
                      >
                        Visit <ArrowRight className="ml-1 h-3 w-3" />
                      </a>
                    </div>
                    <p className="text-sm mb-3 text-gray-700 dark:text-gray-300">
                      {rec.reason}
                    </p>
                    <div className="flex flex-wrap gap-2 text-xs">
                      <span className="px-2 py-1 rounded-full bg-white/70 dark:bg-slate-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-slate-600">
                        <Tag className="h-3 w-3 mr-1 inline" /> {rec.category}
                      </span>
                      {rec.has_free_tier && (
                        <span className="px-2 py-1 rounded-full bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                          <Gift className="h-3 w-3 mr-1 inline" /> Free Tier
                        </span>
                      )}
                      {rec.is_open_source && (
                        <span className="px-2 py-1 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800">
                          <Star className="h-3 w-3 mr-1 inline" /> Open Source
                        </span>
                      )}
                      <span className="px-2 py-1 rounded-full bg-white/70 dark:bg-slate-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-slate-600">
                        <User className="h-3 w-3 mr-1 inline" /> {rec.audience}
                      </span>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          )}

          {msg.followupQuestions && msg.followupQuestions.length > 0 && !hasFollowedUp && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="mt-1"
            >
              <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 ml-1">
                Explore further:
              </div>
              <div className="flex flex-wrap gap-2">
                {msg.followupQuestions.map((q, i) => (
                  <button
                    key={i}
                    onClick={() => handleSendMessage(q)}
                    className="text-xs px-3 py-1.5 rounded-full bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 text-gray-800 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors shadow-sm flex items-center"
                  >
                    {q} <ChevronRight className="h-3 w-3 ml-1" />
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );

  const renderUserMessage = (msg: Message, idx: number) => (
    <motion.div
      key={`user-${idx}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-4 flex justify-end"
    >
      <div className="flex gap-2 max-w-[90%]">
        <div className="px-4 py-3 rounded-2xl bg-gradient-to-r from-indigo-600 to-violet-600 text-white shadow-md">
          <div className="flex items-center gap-2 mb-1 text-xs text-indigo-100">
            <span>You</span>
            <span className="opacity-70">
              {msg.timestamp.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </span>
          </div>
          <div className="text-sm whitespace-pre-wrap">{msg.content}</div>
        </div>
        <div className="mt-1 w-8 h-8 rounded-full bg-indigo-100 dark:bg-slate-700 flex items-center justify-center text-indigo-600 dark:text-indigo-400 border border-indigo-200 dark:border-slate-600">
          <User className="h-4 w-4" />
        </div>
      </div>
    </motion.div>
  );

  return (
    <>
      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} />

      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent
          className="max-w-none rounded-none m-0 flex flex-col shadow-xl overflow-hidden sm:max-w-2xl sm:h-[90vh] sm:top-1/2 sm:left-1/2 sm:translate-x-[-50%] sm:translate-y-[-50%] bg-white dark:bg-slate-900 sm:rounded-2xl p-0"
          style={{ height: viewportHeight }} // Applied dynamic height
        >
          <DialogHeader className="px-4 py-3 border-b bg-white dark:bg-slate-900 relative flex-shrink-0">
            <DialogTitle className="text-lg font-semibold flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="bg-gradient-to-r from-indigo-600 to-violet-600 rounded-lg p-1.5 text-white shadow-sm">
                  <Sparkles className="h-5 w-5" />
                </div>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-violet-600 font-bold">
                  AdMesh Assistant
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger className="h-9 px-3 text-xs w-[150px] border-gray-300 dark:border-slate-600 rounded-full">
                    <SelectValue placeholder="Model" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg shadow-lg border-gray-200 dark:border-slate-700">
                    {models.map((model) => (
                      <SelectItem key={model.id} value={model.id} className="cursor-pointer">
                        <span className="flex items-center">
                          <span
                            className={`w-2 h-2 rounded-full ${model.color} mr-2`}
                          />
                          {model.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {messages.length > 0 && (
                  <button
                    onClick={resetConversation}
                    className="h-9 px-3 rounded-full text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors text-xs flex items-center gap-1"
                  >
                    <RefreshCw className="h-3.5 w-3.5" /> New Chat
                  </button>
                )}
                <button
                  className="h-8 w-8 flex items-center justify-center text-gray-500 dark:text-gray-400"
                >
                  {/* <X className="h-4 w-4" /> */}
                </button>
              </div>
            </DialogTitle>
          </DialogHeader>

          {/* Messages - Updated with proper mobile scrolling */}
          <div
            className="flex-1 overflow-y-auto p-4 bg-gray-50 dark:bg-slate-800 -webkit-overflow-scrolling: touch"
            style={{
              height: `calc(${viewportHeight} - 130px)`,
              maxHeight: `calc(${viewportHeight} - 130px)`,
              overscrollBehavior: "contain",
            }} // Updated for mobile scrolling
          >
            {/* Welcome & Suggested Queries */}
            {messages.length === 0 && (
              <div className="flex flex-col items-center justify-center min-h-full max-h-full overflow-y-auto py-4">
                {/* <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-20 h-20 mb-6 rounded-2xl bg-gradient-to-br from-indigo-600 to-violet-600 flex items-center justify-center shadow-lg"
                >
                  <Sparkles className="h-10 w-10 text-white" />
                </motion.div>
                 */}
                <motion.h2
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-2xl font-bold mb-2 text-gray-900 dark:text-white"
                >
                  Welcome to AdMesh Assistant
                </motion.h2>

                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="text-gray-600 dark:text-gray-300 text-center max-w-md mb-8"
                >
                  Your AI companion for discovering the perfect tools and resources for your projects
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="w-full max-w-md px-4"
                >
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3 text-center">
                    Try asking about:
                  </p>
                  <div className="flex flex-wrap justify-center gap-2">
                    {suggestedQueries.map((q, i) => (
                      <motion.button
                        key={i}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.4 + i * 0.1 }}
                        onClick={() => {
                          localStorage.setItem("pendingQuery", q); // Fixed localStorage usage
                          handleSendMessage(q);
                          if (inputRef.current) inputRef.current.focus();
                        }}
                        className="px-4 py-2 text-sm rounded-full bg-white dark:bg-slate-700 hover:bg-gray-50 dark:hover:bg-slate-600 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-slate-600 shadow-sm transition-all hover:shadow-md"
                      >
                        {q}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              </div>
            )}

            {/* Conversation Messages */}
            <AnimatePresence>
              {messages.map((msg, idx) => (
                msg.role === "user"
                  ? renderUserMessage(msg, idx)
                  : renderBotMessage(msg, idx)
              ))}
            </AnimatePresence>

            {/* Loading Indicator */}
            {isLoading && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center gap-3 px-4 py-3 rounded-2xl bg-white dark:bg-slate-800 shadow-sm border border-gray-100 dark:border-slate-700 max-w-[90%] ml-10"
              >
                <div className="flex space-x-1">
                  <div className="h-2 w-2 bg-indigo-600 rounded-full animate-pulse" />
                  <div className="h-2 w-2 bg-indigo-600 rounded-full animate-pulse delay-150" />
                  <div className="h-2 w-2 bg-indigo-600 rounded-full animate-pulse delay-300" />
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-300">Finding the best tools for you...</span>
              </motion.div>
            )}

            {/* End of Session */}
            {hasFollowedUp && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="text-center mt-8 py-6 px-4 bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-gray-100 dark:border-slate-700"
              >
                <div className="h-12 w-12 mx-auto mb-3 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center">
                  <MessageSquare className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                  End of Session
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  You&apos;ve reached the end of this conversation. Start a new chat to explore more tools.
                </p>
                <Button
                  onClick={resetConversation}
                  className="px-5 py-2 h-auto rounded-full bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700 text-white shadow-md hover:shadow-lg transition-all"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Start New Chat
                </Button>
              </motion.div>
            )}

            <div ref={messagesEndRef} />
          </div>

          <DialogFooter
            className="p-3 md:p-4 border-t bg-white dark:bg-slate-900 dark:border-slate-700 sticky bottom-0 w-full flex-shrink-0 z-10"
            style={{ backdropFilter: "blur(4px)", WebkitBackdropFilter: "blur(4px)" }} // Optional sticky footer fix
          >
            <form
              onSubmit={(e) => {
                e.preventDefault();
                localStorage.setItem("pendingQuery", inputMessage);
                handleSendMessage();
              }}
              className="w-full flex items-end gap-2"
            >
              <Textarea
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                disabled={hasFollowedUp}
                placeholder={
                  hasFollowedUp
                    ? "Session ended. Start a new chat to ask more."
                    : "Ask about tools, resources, or software recommendations..."
                }
                className={cn(
                  "flex-1 resize-none min-h-[52px] max-h-[120px] text-sm px-4 py-3 rounded-full dark:bg-slate-800 dark:border-slate-700 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 focus:border-indigo-500 transition-all",
                  messages.length === 0 && "md:min-h-[60px]"
                )}
              />
              <Button
                type="submit"
                size="icon"
                className={cn(
                  "h-12 w-12 rounded-full bg-gradient-to-r from-indigo-600 to-violet-600 shadow-md hover:shadow-lg hover:from-indigo-700 hover:to-violet-700 transition-all",
                  messages.length === 0 && "md:h-14 md:w-14"
                )}
                disabled={!inputMessage.trim() || isLoading || hasFollowedUp}
              >
                <Send className={cn("h-5 w-5", messages.length === 0 && "md:h-6 md:w-6")} />
              </Button>
            </form>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}