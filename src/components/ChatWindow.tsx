"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import { useCreditsContext } from "@/contexts/credits-context";
import { toast } from "sonner";
import AgentNameEditDialog from "@/components/AgentNameEditDialog";
import AgentInputDialog from "@/components/AgentInputDialog";
import {
  Bot,
  User,
  Send,
  ExternalLink,
  Tag,
  Gift,
  ThumbsUp,
  ThumbsDown,
  ArrowRight,
  Layers,
  DollarSign,
  Calendar,
  MessagesSquare,
  BadgeCheck,
  Sparkles,
  Lock,
  MessageSquarePlus,
  RefreshCw,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { auth } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";
import AuthModal from "@/components/AuthModal";
import { User as FirebaseUser } from "firebase/auth";
import CompareDialog from "@/components/CompareDialog";
import Image from "next/image";
import { marked } from "marked";
import XpGainTrigger from "@/components/XpGainTrigger";
import dynamic from 'next/dynamic';

// Dynamically import AdMeshLayout - styles are auto-injected! ✨
const AdMeshLayout = dynamic(() => import('admesh-ui-sdk').then(mod => ({ default: mod.AdMeshLayout })), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-gray-200 rounded-lg h-48"></div>
});

// Helper function to append tracking parameters to AdMesh links
function appendTrackingParams(url: string, rec_id: string, session_id: string, agent_id: string, user_id: string) {
  // Check if the URL already has query parameters
  const hasQueryParams = url.includes('?');
  const separator = hasQueryParams ? '&' : '?';

  // Construct the final URL with tracking parameters
  const finalUrl = `${url}${separator}rec_id=${encodeURIComponent(rec_id)}&session_id=${encodeURIComponent(session_id)}&agent_id=${encodeURIComponent(agent_id)}&user_id=${encodeURIComponent(user_id)}`;

  return finalUrl;
}
const models = [
  {
    id: "mistralai/mistral-7b-instruct",
    name: "Mistral",
    color: "bg-black",
  },
  {
    id: "google/gemini-2.0-flash-001",
    name: "Gemini 2.0",
    color: "bg-gray-600",
  },
  // {
  //   id: "anthropic/claude-3.7-sonnet",
  //   name: "Claude 3.7",
  //   color: "bg-green-600",
  // },
  // {
  //   id: "openai/gpt-4.1-mini",
  //   name: "GPT-4 Mini",
  //   color: "bg-red-600",
  // },
];

interface Message {
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  recommendations?: Recommendation[];
  followupSuggestions?: string[]; // Renamed from followupQuestions
  responseFormat?: "structured" | "free-form";
  intent?: IntentData;
  decision_factors?: DecisionFactors;
  recommendation_id: string;
  final_verdict?: string; // Added property
  agentRecommendations?: {
    title: string;
    reason: string;
    admesh_link: string;
    ad_id: string;
    product_id: string;
    intent_match_score: number;
    // Rich data fields from backend
    url?: string;
    redirect_url?: string;
    description?: string;
    pricing?: string;
    reward_note?: string;
    keywords?: string[];
    categories?: string[];
    features?: string[];
    integrations?: string[];
    has_free_tier?: boolean;
    trial_days?: number;
    audience_segment?: string;
    is_ai_powered?: boolean;
    is_open_source?: boolean;
    offer_trust_score?: number;
    brand_trust_score?: number;
  }[];
}

export interface Recommendation {
  admesh_link: string;
  offer_id?: string;
  ad_id?: string;
  title: string;
  reason: string;
  url: string;
  category?: string;
  keywords?: string[];
  has_free_tier: boolean;
  is_ai_powered?: boolean;
  is_open_source?: boolean;
  audience?: string;
  pricing?: string | { monthly?: string; annual?: string };
  trial_days?: number;
  features?: string[];
  integrations?: string[];
  support?: string[];
  security?: string[];
  reviews_summary?: string;
  confidence_score?: number;
  trust_score?: number;
  product_id?: string;
  redirect_url?: string;
}

interface IntentData {
  type: string;
  goal?: string[];
  known_mentions?: string[];
  category?: string;
}

interface DecisionFactors {
  highlighted?: string[];
  reasoning?: string;
}

interface ResponseData {
  summary: string;
  recommendations: Recommendation[];
  followup_suggestions?: string[];
  final_verdict?: string;
}

interface OpenRouterApiResponse {
  intent?: IntentData;
  decision_factors?: DecisionFactors;
  response: ResponseData;
  model_used?: string;
  tokens_used?: number;
  session_id?: string;
  recommendation_id?: string;
  rec_product_ids?: string[];
  end_of_session?: boolean;
  error?: string;
}

interface AgentRecommendation {
  title: string;
  reason: string;
  admesh_link: string;
  ad_id: string;
  product_id: string;
  intent_match_score: number;
  // Rich data fields from backend
  url?: string;
  redirect_url?: string;
  description?: string;
  pricing?: string;
  reward_note?: string;
  keywords?: string[];
  categories?: string[];
  features?: string[];
  integrations?: string[];
  has_free_tier?: boolean;
  trial_days?: number;
  audience_segment?: string;
  is_ai_powered?: boolean;
  is_open_source?: boolean;
  offer_trust_score?: number;
  brand_trust_score?: number;
}

interface AgentApiResponse {
  response?: {
    recommendations: AgentRecommendation[];
  };
  error?: string;
}

export default function ChatWindow() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [selectedModel, setSelectedModel] = useState(models[0].id);
  const [previousQuery, setPreviousQuery] = useState<string | null>(null);

  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [authOpen, setAuthOpen] = useState(false);
  const [suggestedQueries, setSuggestedQueries] = useState<string[]>([]);
  const [initialQuery, setInitialQuery] = useState<string | null>(null);
  const [initialSummary, setInitialSummary] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [feedback, setFeedback] = useState<Record<number, string | null>>({});
  const [feedbackText, setFeedbackText] = useState<Record<number, string>>({});
  const [compareOpen, setCompareOpen] = useState(false);
  const [compareData, setCompareData] = useState<Recommendation[]>([]);
  const [selectedForCompare, setSelectedForCompare] = useState<
    Recommendation[]
  >([]);
  const [followupSuggestions, setFollowupSuggestions] = useState<string[]>([]);
  const [followupCount, setFollowupCount] = useState(0);
  const [agentName, setAgentName] = useState<string>("AdMesh Assistant");
  const MAX_FOLLOWUPS = 5;
  const [agentInputRequired, setAgentInputRequired] = useState<{url: string, fields: string[], title: string} | null>(null);
  const [, setAgentLoading] = useState<string | null>(null);

  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const firstRecommendationRef = useRef<HTMLDivElement | null>(null); // Add a ref for the first recommendation
  // Find the selected model metadata
  models.find((m) => m.id === selectedModel)!;
  const agentId = process.env.NEXT_PUBLIC_AGENT_ID || "admesh-website";
  const { credits, updateCredits } = useCreditsContext();
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const hasRunQueryRef = useRef(false);

  // Debug environment variables on component mount
  useEffect(() => {
    console.log("🔍 Environment Variables Debug:");
    console.log("NEXT_PUBLIC_API_BASE_URL:", process.env.NEXT_PUBLIC_API_BASE_URL);
    console.log("NEXT_PUBLIC_AGENT_ID:", process.env.NEXT_PUBLIC_AGENT_ID);
    console.log("NEXT_PUBLIC_AGENT_API_KEY:", process.env.NEXT_PUBLIC_AGENT_API_KEY);
    console.log("All process.env keys:", Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC')));
  }, []);

  // Function to handle "Try with Agent" button click (currently not used but kept for future implementation)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _tryWithAgent = async (url: string, title: string) => {
    if (!user) {
      setAuthOpen(true);
      return;
    }

    // Set loading state for this specific URL
    setAgentLoading(url);

    // Show a toast with loading state
    const toastId = toast.loading("Agent is analyzing the signup page...", {
      description: "This may take a few seconds",
      icon: <Sparkles className="h-4 w-4 animate-pulse" />
    });

    try {
      const token = await user.getIdToken();
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/try-signup`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          url,
          user_id: user.uid,
          session_id: sessionId,
        }),
      });

      const data = await res.json();

      // Clear loading state
      setAgentLoading(null);

      // Dismiss the loading toast
      toast.dismiss(toastId);

      if (data.status === "input_required") {
        // Store the URL and required fields to show in a modal
        setAgentInputRequired({
          url,
          fields: data.fields,
          title
        });
        toast.message(`Agent needs input: ${data.fields.join(", ")}`, {
          description: "Please provide the required information",
          icon: <Sparkles className="h-4 w-4" />
        });
      } else if (data.status === "success") {
        toast.success("Agent successfully signed up!", {
          description: `Successfully signed up for ${title}`,
          icon: <BadgeCheck className="h-4 w-4" />
        });

        // Trigger XP gain for successful automation
        if (user) {
          try {
            const token = await user.getIdToken();
            await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/pioneer/increment-xp`, {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json"
              }
            });
          } catch (error) {
            console.error("Error incrementing XP:", error);
          }
        }
      } else {
        toast.error("Agent failed to sign up. Try manually.", {
          description: data.message || "Unknown error occurred",
        });
      }
    } catch (err) {
      console.error("Agent error:", err);
      setAgentLoading(null);
      toast.dismiss(toastId);
      toast.error("Agent couldn't reach the site.", {
        description: "Please try again later or visit the site manually",
      });
    }
  };


  // Function to submit user inputs for agent
  const submitAgentInputs = async (url: string, inputs: Record<string, string>) => {
    if (!user) {
      setAuthOpen(true);
      return;
    }

    setAgentLoading(url);

    // Show a toast with loading state
    const toastId = toast.loading("Agent is filling the signup form...", {
      description: "This may take a few seconds",
      icon: <Sparkles className="h-4 w-4 animate-pulse" />
    });

    try {
      const token = await user.getIdToken();
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/submit-inputs`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          url,
          inputs,
          user_id: user.uid,
          session_id: sessionId,
        }),
      });

      const data = await res.json();
      setAgentLoading(null);

      // Dismiss the loading toast
      toast.dismiss(toastId);

      if (data.status === "success") {
        toast.success("Agent successfully signed up!", {
          description: data.message || "Your account has been created",
          icon: <BadgeCheck className="h-4 w-4" />
        });

        // Trigger XP gain for successful automation
        if (user) {
          try {
            const token = await user.getIdToken();
            await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/pioneer/increment-xp`, {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json"
              }
            });
          } catch (error) {
            console.error("Error incrementing XP:", error);
          }
        }
      } else {
        toast.error("Agent failed to sign up. Try manually.", {
          description: data.message || "Unknown error occurred",
        });
      }
    } catch (err) {
      console.error("Agent input submission error:", err);
      setAgentLoading(null);
      toast.dismiss(toastId);
      toast.error("Failed to submit information to agent", {
        description: "Please try again later or visit the site manually",
      });
    }
  };
  const handleSendMessage = useCallback(
    async (overrideQuery?: string, isFollowUp = false) => {
      if (!user) {
        setAuthOpen(true);
        return;
      }

      const query = (overrideQuery || inputMessage).trim();
      if (!query || (isFollowUp && followupCount >= MAX_FOLLOWUPS)) return;

      setPreviousQuery(query); // Store the current query as the previous query

      const userMessage: Message = {
        role: "user",
        content: query,
        timestamp: new Date(),
        recommendation_id: "",
      };

      setMessages((prev) => [...prev, userMessage]);
      setInputMessage("");
      setIsLoading(true);

      const payload: Record<string, unknown> = {
        query,
        model: selectedModel,
        agent_id: agentId,
        user_id: user.uid,
        ...(sessionId && { session_id: sessionId }),
      };

      if (isFollowUp && initialQuery && initialSummary) {
        payload.previous_query = initialQuery;
        payload.summary = initialSummary;
      }
      if (isFollowUp) {
        console.log("📤 Follow-up Payload:", payload);
      }
      try {
        // Check if agent API key is configured
        console.log("🔍 Raw NEXT_PUBLIC_AGENT_API_KEY:", process.env.NEXT_PUBLIC_AGENT_API_KEY);
        console.log("🔍 Type of NEXT_PUBLIC_AGENT_API_KEY:", typeof process.env.NEXT_PUBLIC_AGENT_API_KEY);
        console.log("🔍 Length of NEXT_PUBLIC_AGENT_API_KEY:", process.env.NEXT_PUBLIC_AGENT_API_KEY?.length);

        const hasAgentApiKey = process.env.NEXT_PUBLIC_AGENT_API_KEY &&
                              process.env.NEXT_PUBLIC_AGENT_API_KEY !== 'your_agent_api_key_here' &&
                              process.env.NEXT_PUBLIC_AGENT_API_KEY.trim() !== '';
        console.log("🔑 Agent API Key configured:", hasAgentApiKey);
        console.log("🔑 Agent API Key value:", process.env.NEXT_PUBLIC_AGENT_API_KEY ? `${process.env.NEXT_PUBLIC_AGENT_API_KEY.substring(0, 10)}...` : 'undefined');

        // Prepare agent API call if API key is configured
        const agentApiCall = hasAgentApiKey
          ? fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/recommend`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${process.env.NEXT_PUBLIC_AGENT_API_KEY}`
              },
              body: JSON.stringify({
                query: payload.query,
                session_id: payload.session_id,
                previous_query: payload.previous_query,
                previous_summary: payload.summary,
                format: "auto"
              }),
            })
          : Promise.resolve(new Response(JSON.stringify({ error: "Agent API key not configured" }), { status: 404 }));

        if (hasAgentApiKey) {
          console.log("🚀 Making agent API call to:", `${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/recommend`);
        } else {
          console.log("⚠️ Skipping agent API call - no valid API key");
        }

        // Make parallel API calls to both OpenRouter and Agent endpoints
        const [openRouterRes, agentRes] = await Promise.allSettled([
          fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/openrouter/recommend`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          }),
          agentApiCall
        ]);

        // Process OpenRouter response
        let json: OpenRouterApiResponse = {} as OpenRouterApiResponse;
        if (openRouterRes.status === 'fulfilled') {
          json = await openRouterRes.value.json() as OpenRouterApiResponse;
          console.log("OpenRouter Response:", json);

          if (!openRouterRes.value.ok) {
            throw new Error(json?.error || "Unknown error");
          }
        } else {
          console.error("OpenRouter API failed:", openRouterRes.reason);
          throw new Error("OpenRouter API failed");
        }

        // Process Agent response
        let agentRecommendations: AgentRecommendation[] = [];
        if (agentRes.status === 'fulfilled') {
          try {
            const agentJson = await agentRes.value.json() as AgentApiResponse;
            console.log("🤖 Agent Response:", agentJson);
            console.log("🤖 Agent Response Status:", agentRes.value.status);
            console.log("🤖 Agent Response OK:", agentRes.value.ok);

            if (agentRes.value.ok && agentJson?.response?.recommendations) {
              const agentRecs = agentJson.response.recommendations;
              const openRouterRecs = json?.response?.recommendations || [];

              console.log("🤖 Agent Recommendations:", agentRecs);
              console.log("🔄 OpenRouter Recommendations:", openRouterRecs);

              // Match agent recommendations with OpenRouter recommendations
              const matchedProductIds = new Set<string>();
              const matchedTitles = new Set<string>();

              // Inject admesh_link and ad_id into matching OpenRouter recommendations
              openRouterRecs.forEach((rec: Recommendation) => {
                const match = agentRecs.find((agentRec: AgentRecommendation) =>
                  (rec.product_id && agentRec.product_id === rec.product_id) ||
                  (rec.title && agentRec.title === rec.title)
                );

                if (match) {
                  console.log("✅ Found match:", rec.title, "↔️", match.title);
                  rec.admesh_link = match.admesh_link;
                  rec.ad_id = match.ad_id;
                  matchedProductIds.add(match.product_id);
                  matchedTitles.add(match.title);
                } else {
                  console.log("❌ No match for OpenRouter rec:", rec.title);
                }
              });

              // Collect unmatched agent recommendations and add dummy data for useadmesh
              agentRecommendations = agentRecs.filter((agentRec: AgentRecommendation) =>
                !matchedProductIds.has(agentRec.product_id) &&
                !matchedTitles.has(agentRec.title)
              ).map((agentRec: AgentRecommendation) => {
                // Add dummy data if title is "useadmesh" and fields are missing
                if (agentRec.title?.toLowerCase() === "useadmesh") {
                  return {
                    ...agentRec,
                    description: agentRec.description || "AdMesh is an AI-powered recommendation platform that helps users discover the best software tools and services. Connect with trusted brands and get personalized recommendations based on your specific needs.",
                    pricing: agentRec.pricing || "Free tier available, Pro plans starting at $49/month",
                    features: agentRec.features?.length ? agentRec.features : [
                      "AI-Powered Recommendations",
                      "Real-time Product Discovery",
                      "Trust Score System",
                      "Agent Network",
                      "Revenue Sharing",
                      "API Integration",
                      "Analytics Dashboard",
                      "Custom Branding"
                    ],
                    integrations: agentRec.integrations?.length ? agentRec.integrations : [
                      "OpenAI",
                      "Firebase",
                      "Stripe",
                      "Google Analytics",
                      "Zapier",
                      "Slack",
                      "Discord",
                      "REST API"
                    ],
                    has_free_tier: agentRec.has_free_tier !== undefined ? agentRec.has_free_tier : true,
                    trial_days: agentRec.trial_days !== undefined ? agentRec.trial_days : 14,
                    is_ai_powered: agentRec.is_ai_powered !== undefined ? agentRec.is_ai_powered : true,
                    is_open_source: agentRec.is_open_source !== undefined ? agentRec.is_open_source : false,
                    audience_segment: agentRec.audience_segment || "Developers, Marketers, Entrepreneurs",
                    categories: agentRec.categories?.length ? agentRec.categories : ["ad_network", "ai_tools", "marketing", "saas"],
                    keywords: agentRec.keywords?.length ? agentRec.keywords : ["recommendations", "ai", "marketing", "tools", "discovery"],
                    offer_trust_score: agentRec.offer_trust_score !== undefined ? agentRec.offer_trust_score : 0.95,
                    brand_trust_score: agentRec.brand_trust_score !== undefined ? agentRec.brand_trust_score : 0.92,
                    url: agentRec.url || "https://useadmesh.com",
                    redirect_url: agentRec.redirect_url || "https://useadmesh.com",
                    reward_note: agentRec.reward_note || "Earn revenue share for successful referrals"
                  };
                }
                return agentRec;
              });

              console.log("🎯 Final Agent Recommendations (unmatched):", agentRecommendations);
              console.log("🎯 Matched Product IDs:", Array.from(matchedProductIds));
              console.log("🎯 Matched Titles:", Array.from(matchedTitles));
            } else {
              console.log("❌ Agent API response not OK or no recommendations");
              console.log("❌ Response OK:", agentRes.value.ok);
              console.log("❌ Has recommendations:", !!agentJson?.response?.recommendations);
            }
          } catch (error) {
            console.error("❌ Error processing agent response:", error);
          }
        } else {
          console.error("❌ Agent API failed:", agentRes.reason);
        }

        setCurrentSessionId(json.session_id || null);

        const botMessage: Message = {
          role: "assistant",
          content: json?.response?.summary || "Sorry, Please try again.",
          timestamp: new Date(),
          recommendations: json?.response?.recommendations || [],
          followupSuggestions: json?.response?.followup_suggestions || [], // Updated key
          responseFormat: "structured", // Force structured format if recommendations exist
          intent: json?.intent,
          decision_factors: json?.decision_factors,
          recommendation_id: json?.recommendation_id || "",
          final_verdict: json?.response?.final_verdict || "", // ✅ add this line
          agentRecommendations: agentRecommendations,
        };

        // Log the bot message for debugging
        console.log("🤖 Final Bot Message:", botMessage);
        console.log("🤖 Bot Message Agent Recommendations:", botMessage.agentRecommendations);
        console.log("🤖 Agent Recommendations Length:", botMessage.agentRecommendations?.length || 0);

        setMessages((prev) => [...prev, botMessage]);

        if (json?.session_id) setSessionId(json.session_id);
        if (!initialQuery) {
          setInitialQuery(query);
          setInitialSummary(json?.response?.summary || "");
        }
        if (isFollowUp) {
          setFollowupCount((prev) => {
            const nextCount = prev + 1;
            return nextCount >= MAX_FOLLOWUPS ? MAX_FOLLOWUPS : nextCount;
          });
        }

        setTimeout(() => {
          if (firstRecommendationRef.current) {
            firstRecommendationRef.current.scrollIntoView({
              behavior: "smooth",
            });
          }
        }, 100); // Focus on the first recommendation after rendering
      } catch {
        setMessages((prev) => [
          ...prev,
          {
            role: "assistant",
            content: "⚠️ Could not reach the recommendation engine.",
            timestamp: new Date(),
            recommendation_id: "", // Provide a default or placeholder value
          },
        ]);
      } finally {
        setIsLoading(false);
      }
      // Deduct 1 credit using the global context
      if (credits !== null) {
        updateCredits(credits - 1);
      }

      // Trigger XP gain for user queries
      if (user && !isFollowUp) {
        try {
          const token = await user.getIdToken();
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/pioneer/increment-xp`, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json"
            }
          });

          if (!response.ok) {
            console.error("Error incrementing XP:", await response.text());
          }
        } catch (error) {
          console.error("Error incrementing XP:", error);
        }
      }
    },
    [
      user,
      inputMessage,
      followupCount,
      selectedModel,
      agentId,
      sessionId,
      initialQuery,
      initialSummary,
      credits,
      updateCredits,
    ]
  );

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        e.key === "Enter" &&
        !e.shiftKey &&
        inputMessage.trim() &&
        followupCount < MAX_FOLLOWUPS &&
        !isLoading
      ) {
        e.preventDefault();
        handleSendMessage();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [inputMessage, followupCount, isLoading, handleSendMessage]);

  const handleRetryMessage = useCallback(async () => {
    if (!user || !previousQuery) {
      setAuthOpen(true);
      return;
    }

    setIsLoading(true);

    const payload: Record<string, unknown> = {
      query: previousQuery, // Use the stored previous query
      model: selectedModel,
      agent_id: agentId,
      user_id: user.uid,
      ...(sessionId && { session_id: sessionId }),
    };

    if (initialQuery && initialSummary) {
      payload.previous_query = initialQuery;
      payload.summary = initialSummary;
    }

    try {
      // Prepare agent API call if API key is configured for retry
      const hasAgentApiKey = process.env.NEXT_PUBLIC_AGENT_API_KEY &&
                            process.env.NEXT_PUBLIC_AGENT_API_KEY !== 'your_agent_api_key_here' &&
                            process.env.NEXT_PUBLIC_AGENT_API_KEY.trim() !== '';

      const agentApiCall = hasAgentApiKey
        ? fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/recommend`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${process.env.NEXT_PUBLIC_AGENT_API_KEY}`
            },
            body: JSON.stringify({
              query: payload.query,
              session_id: payload.session_id,
              previous_query: payload.previous_query,
              previous_summary: payload.summary,
              format: "auto"
            }),
          })
        : Promise.resolve(new Response(JSON.stringify({ error: "Agent API key not configured" }), { status: 404 }));

      // Make parallel API calls to both OpenRouter and Agent endpoints for retry
      const [openRouterRes, agentRes] = await Promise.allSettled([
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/openrouter/recommend`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        }),
        agentApiCall
      ]);

      // Process OpenRouter response
      let json: OpenRouterApiResponse = {} as OpenRouterApiResponse;
      if (openRouterRes.status === 'fulfilled') {
        json = await openRouterRes.value.json() as OpenRouterApiResponse;
        console.log("Retry OpenRouter Response:", json);

        if (!openRouterRes.value.ok) {
          throw new Error(json?.error || "Unknown error");
        }
      } else {
        console.error("Retry OpenRouter API failed:", openRouterRes.reason);
        throw new Error("OpenRouter API failed");
      }

      // Process Agent response for retry
      let agentRecommendations: AgentRecommendation[] = [];
      if (agentRes.status === 'fulfilled') {
        try {
          const agentJson = await agentRes.value.json() as AgentApiResponse;
          console.log("Retry Agent Response:", agentJson);

          if (agentRes.value.ok && agentJson?.response?.recommendations) {
            const agentRecs = agentJson.response.recommendations;
            const openRouterRecs = json?.response?.recommendations || [];

            // Match agent recommendations with OpenRouter recommendations
            const matchedProductIds = new Set<string>();
            const matchedTitles = new Set<string>();

            // Inject admesh_link and ad_id into matching OpenRouter recommendations
            openRouterRecs.forEach((rec: Recommendation) => {
              const match = agentRecs.find((agentRec: AgentRecommendation) =>
                (rec.product_id && agentRec.product_id === rec.product_id) ||
                (rec.title && agentRec.title === rec.title)
              );

              if (match) {
                rec.admesh_link = match.admesh_link;
                rec.ad_id = match.ad_id;
                matchedProductIds.add(match.product_id);
                matchedTitles.add(match.title);
              }
            });

            // Collect unmatched agent recommendations and add dummy data for useadmesh
            agentRecommendations = agentRecs.filter((agentRec: AgentRecommendation) =>
              !matchedProductIds.has(agentRec.product_id) &&
              !matchedTitles.has(agentRec.title)
            ).map((agentRec: AgentRecommendation) => {
              // Add dummy data if title is "useadmesh" and fields are missing
              if (agentRec.title?.toLowerCase() === "useadmesh") {
                return {
                  ...agentRec,
                  description: agentRec.description || "AdMesh is an AI-powered recommendation platform that helps users discover the best software tools and services. Connect with trusted brands and get personalized recommendations based on your specific needs.",
                  pricing: agentRec.pricing || "Free tier available, Pro plans starting at $49/month",
                  features: agentRec.features?.length ? agentRec.features : [
                    "AI-Powered Recommendations",
                    "Real-time Product Discovery",
                    "Trust Score System",
                    "Agent Network",
                    "Revenue Sharing",
                    "API Integration",
                    "Analytics Dashboard",
                    "Custom Branding"
                  ],
                  integrations: agentRec.integrations?.length ? agentRec.integrations : [
                    "OpenAI",
                    "Firebase",
                    "Stripe",
                    "Google Analytics",
                    "Zapier",
                    "Slack",
                    "Discord",
                    "REST API"
                  ],
                  has_free_tier: agentRec.has_free_tier !== undefined ? agentRec.has_free_tier : true,
                  trial_days: agentRec.trial_days !== undefined ? agentRec.trial_days : 14,
                  is_ai_powered: agentRec.is_ai_powered !== undefined ? agentRec.is_ai_powered : true,
                  is_open_source: agentRec.is_open_source !== undefined ? agentRec.is_open_source : false,
                  audience_segment: agentRec.audience_segment || "Developers, Marketers, Entrepreneurs",
                  categories: agentRec.categories?.length ? agentRec.categories : ["ad_network", "ai_tools", "marketing", "saas"],
                  keywords: agentRec.keywords?.length ? agentRec.keywords : ["recommendations", "ai", "marketing", "tools", "discovery"],
                  offer_trust_score: agentRec.offer_trust_score !== undefined ? agentRec.offer_trust_score : 0.95,
                  brand_trust_score: agentRec.brand_trust_score !== undefined ? agentRec.brand_trust_score : 0.92,
                  url: agentRec.url || "https://useadmesh.com",
                  redirect_url: agentRec.redirect_url || "https://useadmesh.com",
                  reward_note: agentRec.reward_note || "Earn revenue share for successful referrals"
                };
              }
              return agentRec;
            });
          }
        } catch (error) {
          console.error("Error processing retry agent response:", error);
        }
      } else {
        console.error("Retry Agent API failed:", agentRes.reason);
      }

      const botMessage: Message = {
        role: "assistant",
        content: json?.response?.summary || "Sorry, Please try again.",
        timestamp: new Date(),
        recommendations: json?.response?.recommendations || [],
        followupSuggestions: json?.response?.followup_suggestions || [], // Updated key
        responseFormat: "structured", // Force structured format if recommendations exist
        intent: json?.intent,
        decision_factors: json?.decision_factors,
        recommendation_id: json?.recommendation_id || "",
        final_verdict: json?.response?.final_verdict || "",
        agentRecommendations: agentRecommendations,
      };

      // Log the bot message for debugging
      console.log("Retry Bot Message:", botMessage);

      setMessages((prev) => [...prev, botMessage]);
    } catch {
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "⚠️ Could not reach the recommendation engine.",
          timestamp: new Date(),
          recommendation_id: "",
        },
      ]);
    } finally {
      setIsLoading(false);
    }

    // Deduct 1 credit for retry using the global context
    if (credits !== null) {
      updateCredits(credits - 1);
    }
  }, [
    user,
    previousQuery,
    selectedModel,
    agentId,
    sessionId,
    initialQuery,
    initialSummary,
    credits,
    updateCredits,
  ]);

  useEffect(() => {
    const storedQuery = localStorage.getItem("pendingQuery");
    if (!user || !storedQuery || hasRunQueryRef.current) return;

    setInputMessage(storedQuery);
    handleSendMessage(storedQuery); // 🔁 Auto-run the query
    localStorage.removeItem("pendingQuery"); // 🧹 Clean up
    hasRunQueryRef.current = true;
  }, [user, handleSendMessage]);

  // Credits are now managed by the credits context, so we don't need to fetch them separately

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    const unsub = onAuthStateChanged(auth, (u) => setUser(u));
    return () => unsub();
  }, []);

  // Fetch agent name from user profile
  useEffect(() => {
    const fetchAgentName = async () => {
      if (!user) return;

      try {
        const token = await user.getIdToken();
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/me`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.ok) {
          const userData = await response.json();
          if (userData.agentName) {
            setAgentName(userData.agentName);
          }
        }
      } catch (error) {
        console.error("Error fetching agent name:", error);
      }
    };

    fetchAgentName();
  }, [user]);

  const updateAgentName = async (newName: string) => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/update-profile`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          agentName: newName
        })
      });

      if (!response.ok) {
        throw new Error("Failed to update agent name");
      }

      setAgentName(newName);
      toast.success("Agent name updated successfully");
    } catch (error) {
      console.error("Error updating agent name:", error);
      toast.error("Failed to update agent name");
    }
  };

  // Generate a session ID when the component mounts
  useEffect(() => {
    if (!sessionId) {
      const newSessionId = uuidv4();
      setSessionId(newSessionId);
      console.log("Generated new session ID on mount:", newSessionId);
    }
  }, [sessionId]);

  useEffect(() => {
    fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/openrouter/suggest`)
      .then((res) => res.json())
      .then((data) => {
        if (Array.isArray(data.suggestions)) {
          setSuggestedQueries(data.suggestions);
        }
      })
      .catch(() => {
        setSuggestedQueries([
          "Best free tools for cold email campaigns",
          "Affordable CRM for solo founders",
          "Tools with AI-powered analytics",
          "Alternatives to Mailchimp",
        ]);
      });
  }, []);

  const handleFeedback = (
    messageIndex: number,
    value: string,
    rec_id: string
  ) => {
    setFeedback((prev) => ({ ...prev, [messageIndex]: value }));
    console.log("Feedback:", value);
    if (value === "helpful") {
      setFeedbackText((prev) => ({ ...prev, [messageIndex]: "helpful" }));
      submitDetailedFeedback(messageIndex, rec_id);
    }
  };

  const handleFeedbackTextChange = (messageIndex: number, text: string) => {
    setFeedbackText((prev) => ({ ...prev, [messageIndex]: text }));
  };

  const submitDetailedFeedback = async (
    messageIndex: number,
    recommendationId: string
  ) => {
    const feedback = feedbackText[messageIndex] || ""; // Ensure feedback is never undefined
    const payload = {
      message_index: messageIndex,
      feedback, // Always include this field
      session_id: currentSessionId, // optional
      user_id: user?.uid || null,
      agent_id: agentId,
      model_used: selectedModel,
      recommendationId: recommendationId,
    };

    await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/feedback/submit`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      }
    );

    // Hide feedback section for this recommendation
    setFeedback((prev) => ({ ...prev, [messageIndex]: "submitted" }));
    setFeedbackText((prev) => ({ ...prev, [messageIndex]: "" }));
  };

  const resetChat = () => {
    setMessages([]);
    setInputMessage("");
    // Generate a new session ID when starting a new chat
    const newSessionId = uuidv4();
    setSessionId(newSessionId);
    console.log("Generated new session ID for new chat:", newSessionId);
    setInitialQuery(null);
    setInitialSummary(null);
    setFollowupCount(0);
    setFeedback({});
    setFeedbackText({});
    setSelectedForCompare([]);
    setCompareData([]);
  };

  const renderRecommendationUI = (msg: Message, idx: number) => {
    console.log("Rendering recommendations for message:", msg);
    console.log("Message has recommendations:", !!msg.recommendations);
    console.log("Number of recommendations:", msg.recommendations?.length || 0);

    if (!msg.recommendations || msg.recommendations.length === 0) {
      console.log("No recommendations to render");
      return null;
    }

    // This would be our complete response data from API
    // const recommendationData: RecommendationResponseData = {
    //   intent: msg.intent,
    //   model_used: selectedModel,
    //   decision_factors: msg.decision_factors,
    //   response: {
    //     summary: msg.content,
    //     recommendations: msg.recommendations,
    //     followup_suggestions: msg.followupSuggestions, // Updated key
    //   },
    //   session_id: sessionId || undefined,
    //   end_of_session: followupCount >= MAX_FOLLOWUPS,
    // };

    return (
      <div
        className="flex flex-col space-y-4 w-full"
        ref={idx === messages.length - 1 ? firstRecommendationRef : undefined} // Attach ref to the first recommendation
      >
        {/* Intent Section - only shown for recommendation responses */}
        {/* Quick Decision Summary */}
        {/* {recommendationData.intent?.type &&
          recommendationData.response?.recommendations?.length > 0 && (
            <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 text-sm text-yellow-800 dark:text-yellow-300 mb-2">
              Based on your intent (
              <span className="font-semibold">
                {recommendationData.intent.type}
              </span>
              ), I recommend{" "}
              <span className="font-semibold">
                {recommendationData.response.recommendations[0].title}
              </span>{" "}
              for its{" "}
              <span className="italic">
                {recommendationData.decision_factors?.highlighted
                  ?.slice(0, 2)
                  .join(" and ") || "core strengths"}
              </span>
              .
            </div>
          )} */}

        {/* Recommendations */}
        <div className="space-y-4">
          {msg.recommendations.map((rec, i) => (
            <div
              key={i}
              className="relative p-3 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow"
            >
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center gap-2">
                  {i === 0 && (
                    <span className="text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full">
                      Top Match
                    </span>
                  )}
                  <h4 className="font-semibold text-gray-800 dark:text-gray-200">
                    {rec.title}
                  </h4>
                  <span className="text-xs text-gray-400 dark:text-gray-500 ml-2">
                    #{i + 1}
                  </span>

                  <div className="flex gap-2">
                    <XpGainTrigger action="click">
                      <a
                        href={rec.admesh_link ?
                          appendTrackingParams(
                            rec.admesh_link,
                            msg.recommendation_id || "",
                            sessionId || "",
                            agentId,
                            user?.uid || ""
                          ) : rec.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs px-2 py-1 rounded-full bg-black text-white hover:bg-gray-800 flex items-center"
                      >
                        Visit <ExternalLink className="ml-1 h-3 w-3" />
                      </a>
                    </XpGainTrigger>
                    {/* <Button
                      onClick={() => tryWithAgent(rec.url, rec.title)}
                      className="text-xs px-2 py-1 rounded-full bg-blue-600 hover:bg-blue-700 text-white flex items-center"
                      disabled={agentLoading === rec.url}
                    >
                      {agentLoading === rec.url ? (
                        <>
                          <span className="animate-pulse">Processing</span>
                          <div className="ml-1 h-3 w-3 animate-spin rounded-full border-2 border-white border-t-transparent" />
                        </>
                      ) : (
                        <>
                          <div className="flex items-center">
                            <span>Try with Agent</span>
                            <Sparkles className="ml-1 h-3 w-3" />
                            <span className="ml-1 text-[8px] font-medium px-1 py-0.5 rounded-full bg-blue-500 text-white">
                              BETA
                            </span>
                          </div>
                        </>
                      )}
                    </Button> */}
                  </div>
                  <input
                    type="checkbox"
                    checked={selectedForCompare.some(
                      (r) => r.title === rec.title
                    )}
                    onChange={(e) => {
                      if (e.target.checked && selectedForCompare.length >= 3) {
                        alert("You can only compare up to 3 tools.");
                        return;
                      }
                      if (e.target.checked) {
                        setSelectedForCompare((prev) => [...prev, rec]);
                      } else {
                        setSelectedForCompare((prev) =>
                          prev.filter((r) => r.title !== rec.title)
                        );
                      }
                    }}
                    className="h-4 w-4 accent-black cursor-pointer"
                    title="Select to Compare"
                  />
                </div>
              </div>

              <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                {rec.reason}
              </p>
              {/* Confidence Score */}
              {typeof rec.confidence_score === "number" && (
                <div className="mb-3">
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                    <span>Confidence</span>
                    <span>{Math.round(rec.confidence_score * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden">
                    <div
                      className="bg-black h-1.5"
                      style={{ width: `${rec.confidence_score * 100}%` }}
                    />
                  </div>
                </div>
              )}

              <div className="flex flex-wrap gap-2 text-xs mb-2">
                {rec.pricing && typeof rec.pricing === "string" && (
                  <span className="flex items-center text-gray-600 dark:text-gray-400">
                    <DollarSign className="h-3 w-3 mr-1" />
                    {rec.pricing}
                  </span>
                )}

                {rec.pricing && typeof rec.pricing === "object" && (
                  <div className="flex flex-col text-xs text-gray-600 dark:text-gray-400">
                    {rec.pricing.monthly && (
                      <div className="flex items-center">
                        <DollarSign className="h-3 w-3 mr-1" />
                        Monthly: {rec.pricing.monthly}
                      </div>
                    )}
                    {rec.pricing.annual && (
                      <div className="flex items-center">
                        <DollarSign className="h-3 w-3 mr-1" />
                        Annual: {rec.pricing.annual}
                      </div>
                    )}
                  </div>
                )}

                {rec.has_free_tier && (
                  <span className="flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full">
                    <Gift className="h-3 w-3 mr-1" />
                    Free Tier
                  </span>
                )}

                {rec.trial_days && rec.trial_days !== 0 && (
                  <span className="flex items-center text-gray-600 dark:text-gray-400">
                    <Calendar className="h-3 w-3 mr-1" />
                    {rec.trial_days}-day trial
                  </span>
                )}

                {rec.is_ai_powered && (
                  <span className="flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full">
                    <Sparkles className="h-3 w-3 mr-1" />
                    AI-Powered
                  </span>
                )}

                {rec.is_open_source && (
                  <span className="flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full">
                    <Tag className="h-3 w-3 mr-1" />
                    Open Source
                  </span>
                )}
              </div>

              {/* Features */}
              {rec.features && rec.features.length > 0 && (
                <div className="mb-2">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                    Features:
                  </div>
                  <div className="flex flex-wrap gap-1.5">
                    {rec.features.map((feature, j) => (
                      <span
                        key={j}
                        className="text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300"
                      >
                        <BadgeCheck className="h-3 w-3 mr-0.5 inline text-gray-500" />
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Integrations */}
              {rec.integrations && rec.integrations.length > 0 && (
                <div className="mb-2">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                    Integrates with:
                  </div>
                  <div className="flex flex-wrap gap-1.5">
                    {rec.integrations.map((integration, j) => (
                      <span
                        key={j}
                        className="text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300"
                      >
                        <Layers className="h-3 w-3 mr-0.5 inline" />
                        {integration}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Security */}
              {rec.security && rec.security.length > 0 && (
                <div className="mb-2">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                    Security:
                  </div>
                  <div className="flex flex-wrap gap-1.5">
                    {rec.security.map((security, j) => (
                      <span
                        key={j}
                        className="text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300"
                      >
                        <Lock className="h-3 w-3 mr-0.5 inline" />
                        {security}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Reviews summary */}
              {rec.reviews_summary && (
                <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                  {rec.reviews_summary}
                </div>
              )}
            </div>
          ))}
        </div>
        {/* AdMesh Agent Recommendations */}
        {msg.agentRecommendations && msg.agentRecommendations.length > 0 && (
          <AdMeshLayout
            recommendations={msg.agentRecommendations}
            intentType="best_for_use_case"
            theme={{ mode: "light" }}
            maxDisplayed={6}
            showMatchScores={true}
            showFeatures={true}
            autoLayout={true}
            className="mt-4"
            onProductClick={(_adId: string, admeshLink: string) => {
              // Track the click with XP gain
              if (user) {
                // Trigger XP gain
                const xpEvent = new CustomEvent('xp-gain', { detail: { action: 'click' } });
                window.dispatchEvent(xpEvent);
              }

              // Open the tracked link with proper tracking parameters
              const trackedUrl = appendTrackingParams(
                admeshLink,
                msg.recommendation_id || "",
                sessionId || "",
                agentId,
                user?.uid || ""
              );
              window.open(trackedUrl, '_blank');
            }}
            onTrackView={(data: { adId: string; productId?: string }) => {
              // Track view events
              console.log('Product viewed:', data);
            }}
          />
        )}

        {/* Final Verdict */}
        {msg.agentRecommendations?.length === 0 && msg.final_verdict && (
          <div className="mt-6 p-4 rounded-lg bg-gray-50 dark:bg-gray-900/10 text-sm text-gray-800 dark:text-gray-300 border border-gray-200 dark:border-gray-700 relative">
            <div className="absolute -top-2 left-0 bg-black text-white rounded-full p-1">
              <Sparkles className="h-4 w-4" />
            </div>
            <span className="font-semibold">AdMesh Pick:</span>{" "}
            {msg.final_verdict}
          </div>
        )}

        {/* Follow-up Questions */}
        {msg.followupSuggestions &&
          msg.followupSuggestions.length > 0 &&
          followupCount < MAX_FOLLOWUPS && (
            <div className="mt-2">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                <MessagesSquare className="h-3 w-3 mr-1" />
                Explore further:
              </div>
              <div className="flex flex-wrap gap-2">
                {msg.followupSuggestions.map((q, i) => (
                  <XpGainTrigger key={i} action="followup">
                    <button
                      onClick={() => handleSendMessage(q, true)}
                      className="text-xs px-3 py-1.5 rounded-full bg-white dark:bg-slate-700 border
                    border-gray-200 dark:border-slate-600 text-gray-700 dark:text-gray-200
                    hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors flex items-center"
                    >
                      {q}
                      <ArrowRight className="ml-1 h-3 w-3" />
                    </button>
                  </XpGainTrigger>
                ))}
              </div>
            </div>
          )}

        {/* Feedback Section */}
        {feedback[idx] !== "submitted" && feedback[idx] !== "completed" && (
          <div className="mt-3 pt-2 border-t border-gray-100 dark:border-slate-700">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
              Was this response helpful?
            </p>
            <div className="flex gap-2">
              <button
                onClick={() =>
                  handleFeedback(idx, "helpful", msg.recommendation_id)
                }
                className={`px-3 py-1 rounded-full text-xs flex items-center ${
                  feedback[idx] === "helpful"
                    ? "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 border border-gray-200 dark:border-gray-900"
                    : "bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-600"
                }`}
              >
                <ThumbsUp className="h-3 w-3 mr-1" />
                Yes, thanks!
              </button>
              <button
                onClick={() =>
                  handleFeedback(idx, "not-helpful", msg.recommendation_id)
                }
                className={`px-3 py-1 rounded-full text-xs flex items-center ${
                  feedback[idx] === "not-helpful"
                    ? "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 border border-gray-200 dark:border-gray-900"
                    : "bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-600"
                }`}
              >
                <ThumbsDown className="h-3 w-3 mr-1" />
                Not really
              </button>
            </div>

            {feedback[idx] === "not-helpful" && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-3 p-3 bg-gray-50 dark:bg-slate-800 rounded-lg"
              >
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                  How could we improve?
                </p>
                <Textarea
                  value={feedbackText[idx] || ""}
                  onChange={(e) =>
                    handleFeedbackTextChange(idx, e.target.value)
                  }
                  className="w-full p-2 text-xs border border-gray-200 dark:border-slate-600 rounded-lg
                  dark:bg-slate-700 dark:text-gray-200"
                  placeholder="Tell us what would make this response more helpful..."
                  rows={2}
                />
                <Button
                  onClick={() =>
                    submitDetailedFeedback(idx, msg.recommendation_id)
                  }
                  className="mt-2 px-3 py-1 text-xs bg-black hover:bg-gray-800 text-white rounded-lg"
                >
                  Submit Feedback
                </Button>
              </motion.div>
            )}
          </div>
        )}

        {feedback[idx] === "submitted" && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-3 p-2 bg-gray-50 dark:bg-gray-900/20 rounded-lg relative"
          >
            <p className="text-xs text-gray-700 dark:text-gray-400">
              Thank you for your feedback!
            </p>
            <button
              onClick={() =>
                setFeedback((prev) => ({ ...prev, [idx]: "completed" }))
              }
              className="absolute top-1 right-2 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 text-xs"
              title="Close"
            >
              ✕
            </button>
          </motion.div>
        )}
      </div>
    );
  };

  useEffect(() => {
    if (!isLoading && followupCount < MAX_FOLLOWUPS) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isLoading, followupCount]);

  return (
    <>
      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} />

      <div
        className="w-full flex flex-col bg-white dark:bg-slate-900 border border-gray-200 dark:border-slate-700"
        style={{
          height: "100vh", // Fullscreen height
        }}
      >
        {/* Sticky Header */}
        <div
          className="px-4 py-3 border-b bg-white dark:bg-slate-900 flex justify-between items-center sticky top-0 z-10"
          style={{
            flexShrink: 0, // Prevent shrinking of the header
          }}
        >
          <div className="flex items-center gap-2 hidden sm:flex">
            <div className="w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-900/30 rounded-full flex items-center justify-center">
              <Image src="/logo.svg" alt="Logo" width={32} height={32} />
            </div>
            <div className="flex items-center gap-1">
              <span className="bg-clip-text font-inter font-bold">
                {agentName}
              </span>
              <AgentNameEditDialog
                currentName={agentName}
                onSave={updateAgentName}
              />
            </div>
          </div>
          <Button
            onClick={resetChat}
            className="flex items-center gap-1 px-3 py-1.5 text-xs rounded-full bg-black hover:bg-gray-800 text-white shadow-sm hidden sm:flex"
          >
            <MessageSquarePlus className="h-4 w-4" />
            New Chat
          </Button>
          <Button
            onClick={resetChat}
            className="sm:hidden flex items-center justify-center w-10 h-10 rounded-full bg-black hover:bg-gray-800 text-white shadow-sm"
          >
            <MessageSquarePlus className="h-5 w-5" />
          </Button>
          <div className="flex gap-2 items-center">
            {/* <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Credits: {credits}
            </span> */}
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="h-8 w-28 px-2 text-xs border-gray-300 dark:border-slate-600">
                <SelectValue placeholder="Model" />
              </SelectTrigger>
              <SelectContent>
                {models.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <span
                      className={`w-2 h-2 rounded-full ${model.color} mr-2 inline-block`}
                    />
                    {model.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Messages Container */}
        <div
          className="flex-1 overflow-y-auto px-4 py-3 bg-gray-50 dark:bg-slate-800"
          style={{
            height: "calc(100% - 130px)", // Adjust for header and footer
            maxHeight: "calc(100% - 130px)",
            WebkitOverflowScrolling: "touch",
            overscrollBehavior: "contain",
          }}
        >
          {selectedForCompare.length >= 1 && (
            <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50">
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => {
                    setCompareData(selectedForCompare);
                    setCompareOpen(true);
                    setFollowupSuggestions(
                      messages[messages.length - 1]?.followupSuggestions || []
                    );
                  }}
                  className="text-sm px-4 py-2 rounded-full bg-black hover:bg-gray-800 text-white shadow-lg"
                >
                  Compare Selected ({selectedForCompare.length})
                </Button>
                <button
                  onClick={() => setSelectedForCompare([])}
                  className="text-sm px-2 py-1 rounded-full bg-black hover:bg-gray-800 text-white shadow-lg"
                  title="Cancel Compare"
                >
                  ✕
                </button>
              </div>
            </div>
          )}
          {messages.length === 0 && (
            <div className="text-center text-gray-400 mt-10 dark:text-gray-500">
              <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-900/30 rounded-full flex items-center justify-center">
                <Image src="/logo.svg" alt="Logo" width={60} height={60} />
              </div>
              <div className="flex items-center justify-center gap-1 mt-2">
                <h2 className="text-sm font-medium">
                  {agentName}
                </h2>
                <AgentNameEditDialog
                  currentName={agentName}
                  onSave={updateAgentName}
                />
              </div>
              <p className="text-sm font-medium mt-2">
                Start with a question or try one of these:
              </p>
              <div className="mt-4 flex flex-wrap justify-center gap-2">
                {suggestedQueries.map((q, i) => (
                  <button
                    key={i}
                    onClick={() => handleSendMessage(q)}
                    className="px-3 py-1 text-xs rounded-full bg-white dark:bg-slate-700 hover:bg-white text-gray-700 dark:text-gray-200
                    border border-gray-200 dark:border-slate-600 shadow-sm"
                  >
                    {q}
                  </button>
                ))}
              </div>
            </div>
          )}

          <AnimatePresence>
            {messages.map((msg, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 8 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className={`mb-4 flex ${
                  msg.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`relative px-3 py-2 ${
                    msg.recommendations?.length
                      ? "max-w-full w-full"
                      : "max-w-[90%]"
                  } rounded-lg text-sm shadow-sm ${
                    msg.role === "user"
                      ? "bg-black text-white"
                      : "bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 text-gray-800 dark:text-gray-200"
                  }`}
                >
                  <div className="text-xs text-white text-muted-foreground mb-1 flex items-center">
                    {msg.role === "user" ? (
                      <User className="h-3 w-3 mr-1" />
                    ) : (
                      <Bot className="h-3 w-3 mr-1" />
                    )}
                    {msg.role === "user" ? "You" : agentName}
                    <span className="ml-2 opacity-70">
                      {msg.timestamp.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>

                  <div className="whitespace-pre-wrap mb-2">
                    {msg.role === "user" ? (
                      <div className="whitespace-pre-wrap mb-2">
                        {msg.content}
                      </div>
                    ) : null}

                    {msg.content === "Sorry, Please try again." &&
                      msg.role === "assistant" && (
                        <button
                          onClick={handleRetryMessage}
                          className="ml-2 text-indigo-600 hover:text-indigo-800"
                          title="Retry"
                        >
                          Sorry, please try again.
                          <RefreshCw className="h-4 w-4 inline" />
                        </button>
                      )}
                  </div>

                  {/* Render our new recommendation UI */}
                  {msg.role === "assistant" && (
                    <>
                      {console.log("Message role:", msg.role)}
                      {console.log("Response format:", msg.responseFormat)}
                      {console.log("Has recommendations:", !!msg.recommendations && msg.recommendations.length > 0)}

                      {msg.responseFormat === "structured" ? (
                        renderRecommendationUI(msg, idx)
                      ) : (
                        <>
                          {console.log("Free-form response message:", msg)}
                          <div
                            className="mt-2 text-sm text-gray-800 dark:text-gray-200"
                            dangerouslySetInnerHTML={{
                              __html: marked.parse(msg.content),
                            }}
                          />
                        </>
                      )}
                    </>
                  )}
                </div>
              </motion.div>
            ))}
          </AnimatePresence>

          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mt-3 text-sm text-gray-500 flex items-center gap-2"
            >
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 bg-black rounded-full animate-pulse" />
                <div className="h-2 w-2 bg-black rounded-full animate-pulse delay-150" />
                <div className="h-2 w-2 bg-black rounded-full animate-pulse delay-300" />
              </div>
              <span>Thinking...</span>
            </motion.div>
          )}

          {followupCount >= MAX_FOLLOWUPS && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="text-center mt-6"
            >
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                You&apos;ve reached the end of this session. Start a new chat to
                ask something else.
              </p>
              <Button
                onClick={resetChat}
                className="text-xs px-4 py-2 rounded-full bg-black hover:bg-gray-800 text-white"
              >
                Start New Session
              </Button>
            </motion.div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Sticky Footer */}
        <div
          className="p-3 border-t bg-white dark:bg-slate-900 dark:border-slate-700 sticky bottom-0 z-10 w-full"
          style={{
            flexShrink: 0, // Prevent shrinking of the footer
            backdropFilter: "blur(4px)",
            WebkitBackdropFilter: "blur(4px)",
          }}
        >
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSendMessage();
            }}
            className="w-full flex gap-2"
          >
            <Textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Ask something like 'best AI tools for email marketing'..."
              className="flex-1 resize-none min-h-[44px] max-h-[120px] text-sm px-4 py-3 rounded-full dark:bg-slate-800 dark:border-slate-700"
              disabled={followupCount >= MAX_FOLLOWUPS || isLoading}
            />
            <Button
              type="submit"
              size="icon"
              className="h-11 w-11 rounded-full bg-black shadow-md"
              disabled={
                !inputMessage.trim() ||
                followupCount >= MAX_FOLLOWUPS ||
                isLoading
              }
            >
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </div>
      </div>
      <CompareDialog
        open={compareOpen}
        onClose={() => setCompareOpen(false)}
        recommendations={compareData}
        followupSuggestions={followupSuggestions} // Updated key
        sessionId={sessionId || ""}
        recommendationId={messages.length > 0 ? messages[messages.length - 1].recommendation_id : ""}
        onAskFollowup={(query) => {
          handleSendMessage(query, true);
          setCompareOpen(false);
          setSelectedForCompare([]);
          setCompareData([]);
        }}
      />

      {/* Agent Input Dialog */}
      <AgentInputDialog
        open={!!agentInputRequired}
        onClose={() => setAgentInputRequired(null)}
        data={agentInputRequired}
        onSubmit={submitAgentInputs}
      />
    </>
  );
}
