"use client";

import { usePathname } from "next/navigation";
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Chatbot from "@/components/Chatbot";
import { Toaster } from "@/components/ui/sonner";
import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { ThemeProvider } from "next-themes";
import Script from "next/script";
import { MainPageStructuredData } from "@/components/StructuredData";
import CookiePreferences from "@/components/CookiePreferences";

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isDashboard = pathname.startsWith("/dashboard");
  const showNavbar = !isDashboard;
  const isRootPage = pathname === "/";

  // Clear cookie consent status when component mounts on root page
  useEffect(() => {
    if (isRootPage) {
      // Clear the cookie consent status to show the banner on each new visit
      localStorage.removeItem("cookie_consent_status");

      // Set a flag in sessionStorage to track this visit
      // This ensures the banner shows on each new browser session but not on page refreshes
      if (!sessionStorage.getItem("current_visit")) {
        sessionStorage.setItem("current_visit", "true");
      }
    }
  }, [isRootPage]);

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {/* Google Tag Manager - Replace with your GTM ID */}
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-XXXXXXX');
          `,
        }}
      />

      {/* Add page-specific structured data for main pages */}
      {(pathname === "/" || pathname === "/brands" || pathname === "/agents" || pathname === "/users") && (
        <MainPageStructuredData />
      )}

      {showNavbar && <Navbar />}
      <main>{children}</main>
      <Analytics />
      <SpeedInsights />
      <Toaster position="bottom-right" richColors closeButton />

      {/* Chatbot Component - only shows on landing pages */}
      <Chatbot />

      {/* Cookie Preferences Component */}
      <CookiePreferences />

      {/* Google Tag Manager noscript (for browsers with JavaScript disabled) */}
      <noscript>
        <iframe
          src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXXXX"
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </ThemeProvider>
  );
}
