"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";

interface CookieConsentBannerProps {
  onAccept: () => void;
  onDeny: () => void;
}

export default function CookieConsentBanner({
  onAccept,
  onDeny,
}: CookieConsentBannerProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Small delay to prevent flash on initial load
    const timer = setTimeout(() => {
      // Check if the banner should be shown
      // Only show if:
      // 1. No cookie consent status is set in localStorage
      // 2. This is a new visit (tracked by sessionStorage)
      const hasConsentStatus = localStorage.getItem("cookie_consent_status");
      const isNewVisit = sessionStorage.getItem("current_visit") === "true";

      const shouldShowBanner = !hasConsentStatus && isNewVisit;
      setIsVisible(shouldShowBanner);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleAccept = () => {
    setIsVisible(false);
    onAccept();
  };

  const handleDeny = () => {
    setIsVisible(false);
    onDeny();
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 80, x: 80, opacity: 0 }}
          animate={{ y: 0, x: 0, opacity: 1 }}
          exit={{ y: 80, x: 80, opacity: 0 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 300, damping: 25 }}
          className="fixed bottom-4 right-4 z-50 max-w-sm shadow-lg"
        >
          <div className="rounded-lg bg-card/90 backdrop-blur-md border border-border overflow-hidden">
            <div className="p-4 relative">
              {/* Close button */}
              <button
                onClick={handleDeny}
                className="absolute top-2 right-2 text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Close cookie notice"
              >
                <X className="h-4 w-4" />
              </button>

              <div className="flex items-start gap-3 mb-3">
                <Cookie className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-base font-medium mb-1">Cookie Notice</h3>
                  <p className="text-sm text-muted-foreground">
                    We use cookies to enhance your browsing experience. By clicking &quot;Accept&quot;, you consent to our use of cookies as described in our <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>.
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeny}
                  className="flex-1"
                >
                  Deny
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleAccept}
                  className="flex-1"
                >
                  Accept
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
