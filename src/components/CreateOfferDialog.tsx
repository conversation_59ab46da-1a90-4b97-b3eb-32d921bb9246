"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useState } from "react";

export default function CreateOfferDialog() {
  const [open, setOpen] = useState(false);

  const [title, setTitle] = useState("");
  const [url, setUrl] = useState("");
  const [rewardNote, setRewardNote] = useState("");
  const [budget, setBudget] = useState("");
  const [categories, setCategories] = useState("");
  const [keywords, setKeywords] = useState("");

  const [payoutAmount, setPayoutAmount] = useState("");
  const [payoutCurrency, setPayoutCurrency] = useState("USD");
  const [payoutModel, setPayoutModel] = useState("CPA");

  const [isActive, setIsActive] = useState(false);  // Set offers as inactive by default

  const handleSubmit = () => {
    // Validation
    if (!title || !url || !payoutAmount || !budget) {
      alert("Please fill in all required fields");
      return;
    }

    const payoutAmountCents = Number(payoutAmount) * 100; // Convert to cents
    const budgetCents = Number(budget) * 100; // Convert to cents

    if (payoutAmountCents <= 0 || budgetCents <= 0) {
      alert("Payout amount and budget must be greater than 0");
      return;
    }

    if (budgetCents < payoutAmountCents) {
      alert("Budget must be at least equal to the payout amount");
      return;
    }

    const newOffer = {
      title,
      url,
      reward_note: rewardNote,
      total_budget_allocated: budgetCents, // Store in cents
      categories: categories.split(",").map((c) => c.trim()).filter(c => c),
      keywords: keywords.split(",").map((k) => k.trim()).filter(k => k),
      payout: {
        amount: payoutAmountCents, // Store in cents
        currency: payoutCurrency,
        model: payoutModel,
      },
      total_spent: {
        test: 0,
        production: 0,
        total: 0
      },
      offer_trust_score: 100, // Set default offer_trust_score
      offer_views: {
        test: 0,
        production: 0,
        total: 0
      },
      active: isActive,
      created_at: new Date().toISOString(),
    };

    console.log("Creating offer:", newOffer);

    // TODO: send to Firestore

    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>+ New Offer</Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-xl space-y-6">
        <DialogHeader>
          <DialogTitle>Create New Offer</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label>Title</Label>
            <Input value={title} onChange={(e) => setTitle(e.target.value)} />
          </div>
          <div>
            <Label>Offer URL</Label>
            <Input value={url} onChange={(e) => setUrl(e.target.value)} />
          </div>
          <div className="md:col-span-2">
            <Label>Reward Note</Label>
            <Textarea
              value={rewardNote}
              onChange={(e) => setRewardNote(e.target.value)}
              placeholder="e.g. Earn $10 per signup"
            />
          </div>
          <div>
            <Label>Budget (USD)</Label>
            <Input
              type="number"
              value={budget}
              onChange={(e) => setBudget(e.target.value)}
              placeholder="e.g. 200"
              min="0"
              step="0.01"
            />
            <div className="text-xs text-muted-foreground mt-1">
              Total budget for this offer in dollars
            </div>
          </div>
          <div>
            <Label>Categories (comma separated)</Label>
            <Input
              value={categories}
              onChange={(e) => setCategories(e.target.value)}
              placeholder="Design Tools, Marketing"
            />
          </div>
          <div className="md:col-span-2">
            <Label>Keywords</Label>
            <Input
              value={keywords}
              onChange={(e) => setKeywords(e.target.value)}
              placeholder="ai, design, freelancers"
            />
          </div>
        </div>

        <div className="border-t pt-4 space-y-4">
          <h4 className="font-medium text-sm">Payout Details</h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <Label>Amount (USD)</Label>
              <Input
                type="number"
                value={payoutAmount}
                onChange={(e) => setPayoutAmount(e.target.value)}
                placeholder="e.g. 10.00"
                min="0"
                step="0.01"
              />
              <div className="text-xs text-muted-foreground mt-1">
                Payout per conversion in dollars
              </div>
            </div>
            <div>
              <Label>Currency</Label>
              <Input value={payoutCurrency} onChange={(e) => setPayoutCurrency(e.target.value)} />
            </div>
            <div>
              <Label>Model</Label>
              <Input value={payoutModel} onChange={(e) => setPayoutModel(e.target.value)} />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between pt-2 border-t">
          <Label>Status</Label>
          <Switch checked={isActive} onCheckedChange={setIsActive} />
        </div>

        <DialogFooter>
          <Button onClick={handleSubmit}>Create Offer</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
