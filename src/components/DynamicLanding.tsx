"use client";

import { useState } from "react";
import RoleSelector from "@/components/RoleSelector";
import UserContent from "@/components/UserContent";
import BrandContent from "@/components/BrandContent";
import AgentContent from "@/components/AgentContent";
import Footer from "@/components/Footer";
import { AnimatePresence, motion } from "framer-motion";

type Role = "user" | "brand" | "agent" | null;

export default function DynamicLanding() {
  const [selectedRole, setSelectedRole] = useState<Role>(null);

  const handleRoleSelect = (role: Role) => {
    setSelectedRole(role);
    // Scroll to top when role is selected
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const renderContent = () => {
    switch (selectedRole) {
      case "user":
        return <UserContent onRoleChange={handleRoleSelect} />;
      case "brand":
        return <BrandContent onRoleChange={handleRoleSelect} />;
      case "agent":
        return <AgentContent onRoleChange={handleRoleSelect} />;
      default:
        return <RoleSelector onRoleSelect={handleRoleSelect} />;
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedRole || "selector"}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="flex-grow"
        >
          {renderContent()}
        </motion.div>
      </AnimatePresence>

      {selectedRole && (
        <div className="sticky bottom-0 w-full bg-background border-t border-border p-4 flex justify-center">
          <button
            onClick={() => setSelectedRole(null)}
            className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 12H5" />
              <path d="M12 19l-7-7 7-7" />
            </svg>
            Change role selection
          </button>
        </div>
      )}

      <Footer />
    </div>
  );
}
