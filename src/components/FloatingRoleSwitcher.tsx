"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { User, Building2, Bot, Plus, X } from "lucide-react";

type Role = "user" | "brand" | "agent" | null;

interface FloatingRoleSwitcherProps {
  currentRole: Role;
  onRoleChange: (role: Role) => void;
}

export default function FloatingRoleSwitcher({
  currentRole,
  onRoleChange,
}: FloatingRoleSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);

  const roles = [
    {
      id: "user",
      label: "User",
      icon: <User size={18} />,
      color: "bg-black",
    },
    {
      id: "brand",
      label: "Brand",
      icon: <Building2 size={18} />,
      color: "bg-zinc-800",
    },
    {
      id: "agent",
      label: "Agent",
      icon: <Bot size={18} />,
      color: "bg-zinc-900",
    },
  ];

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleRoleSelect = (role: Role) => {
    onRoleChange(role);
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-6 left-6 z-50">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute bottom-16 left-0 bg-white rounded-lg shadow-xl p-2 mb-2 border border-zinc-200 w-56 sm:w-64 z-50"
            initial={{ opacity: 0, scale: 0.9, y: 5 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 5 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
          >
            <div className="flex flex-col gap-1">
              <div className="text-xs text-zinc-500 px-3 py-2 mb-1 font-medium">
                Switch to:
              </div>
              {roles
                .filter((role) => role.id !== currentRole)
                .map((role) => (
                  <motion.button
                    key={role.id}
                    className="flex items-center gap-3 p-3 rounded-md text-left hover:bg-zinc-100 active:bg-zinc-200 transition-colors"
                    onClick={() => handleRoleSelect(role.id as Role)}
                    whileHover={{ x: 3 }}
                  >
                    <span className="flex items-center justify-center w-8 h-8 bg-zinc-900 text-white rounded-md">{role.icon}</span>
                    <span className="font-medium text-sm text-zinc-800">{role.label}</span>
                  </motion.button>
                ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        className={`px-4 h-10 sm:h-12 rounded-full ${
          currentRole === "user"
            ? "bg-black"
            : currentRole === "brand"
            ? "bg-zinc-800"
            : currentRole === "agent"
            ? "bg-zinc-900"
            : "bg-black"
        } shadow-lg flex items-center justify-center text-white relative`}
        onClick={toggleMenu}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        style={{ boxShadow: "0 8px 12px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }}
      >
        <span className="text-sm font-medium mr-1">Switch</span>

        {isOpen ? (
          <motion.span
            className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-black rounded-full flex items-center justify-center text-white shadow-md"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
          >
            <X size={10} />
          </motion.span>
        ) : (
          <motion.span
            className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-white border border-zinc-300 rounded-full flex items-center justify-center text-black shadow-md"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
          >
            <Plus size={10} />
          </motion.span>
        )}
      </motion.button>
    </div>
  );
}
