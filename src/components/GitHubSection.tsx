"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Github } from "lucide-react";

export default function GitHubSection() {
  return (
    <section className="w-full bg-neutral-50 py-20">
      <div className="mx-auto max-w-4xl px-6 text-center">
        <h2 className="text-3xl font-bold sm:text-4xl mb-6">
          Built for Developers
        </h2>
        <p className="text-muted-foreground mb-8 max-w-xl mx-auto">
          Open protocol. Transparent incentives. Simple integration.
          Explore the SDKs and protocol design on GitHub.
        </p>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <a
            href="https://github.com/admesh-protocol"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button size="lg" variant="outline">
              <Github className="mr-2 h-4 w-4" />
              View on GitHub
            </Button>
          </a>

          {/* Placeholder badges */}
          <div className="text-sm text-muted-foreground">
            SDKs: <span className="font-medium">React</span> · Node · Python (coming soon)
          </div>
        </div>
      </div>
    </section>
  );
}
