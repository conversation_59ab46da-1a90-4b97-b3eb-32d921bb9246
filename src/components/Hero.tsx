"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { auth } from "@/lib/firebase";
import { User as FirebaseUser } from "firebase/auth";
import ChatBox from "@/components/ChatBox";
import AuthModal from "./AuthModal";
import { useRouter } from "next/navigation";

const exampleQueries = [
  "Affordable AI image tools",
  "SEO tools for writers",
  "Best GPTs for productivity",
  "Startup CRM under $50",
  "Coding assistants for VS Code",
];

export default function Hero() {
  const [query, setQuery] = useState("");
  const [displayText, setDisplayText] = useState("");
  const [currentQueryIndex, setCurrentQueryIndex] = useState(0);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [isAnimatingOut, setIsAnimatingOut] = useState(false);
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((u) => setUser(u));
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (isAnimatingOut) {
      const words = displayText.split(" ");
      if (words.length > 0) {
        const timer = setTimeout(() => {
          words.pop();
          setDisplayText(words.join(" "));
          if (words.length === 0) {
            setIsAnimatingOut(false);
            setCurrentQueryIndex((prev) => (prev + 1) % exampleQueries.length);
            setCurrentWordIndex(0);
          }
        }, 300);
        return () => clearTimeout(timer);
      }
    } else {
      const words = exampleQueries[currentQueryIndex].split(" ");
      if (currentWordIndex < words.length) {
        const timer = setTimeout(() => {
          setDisplayText(words.slice(0, currentWordIndex + 1).join(" "));
          setCurrentWordIndex((i) => i + 1);
        }, 100);
        return () => clearTimeout(timer);
      } else {
        const timer = setTimeout(() => setIsAnimatingOut(true), 1800);
        return () => clearTimeout(timer);
      }
    }
  }, [displayText, currentQueryIndex, currentWordIndex, isAnimatingOut]);
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (user) {
      router.push("/dashboard"); // Redirect to dashboard which will handle routing based on role
    } else {
      setIsAuthModalOpen(true);
    }
  };
  return (
    <section className="min-h-screen grid grid-cols-1 md:grid-cols-2 overflow-hidden">
      {/* Left side: Hero content */}
      <div className="flex items-center justify-center bg-gradient-to-b from-indigo-100 to-blue-50 px-6 py-12">
        <div className="text-center max-w-xl w-full">
          <span className="inline-block px-4 py-1.5 mb-6 text-sm font-medium rounded-full bg-accent text-accent-foreground shadow-sm">
            🎉 500 Free Credits for Early Users
          </span>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight leading-tight mb-6">
            Your Personal Agent.
          </h1>
          <p className="mt-2 text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto">
          Don’t just search. Discover with purpose — and earn when you show intent.
          </p>

          {isMobile && (
            <div className="mt-8">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit(e);
                }}
                className="flex flex-col sm:flex-row gap-3 mt-6"
              >
                <input
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder={displayText || "What tool are you looking for?"}
                  className="input-like w-full text-sm placeholder:text-muted-foreground"
                />
                <Button
                  type="submit"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white"
                >
                  Try AdMesh
                </Button>
              </form>
            </div>
          )}

          <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
            <Link href="#how-it-works">
              <Button
                variant="outline"
                size="lg"
                className="w-full sm:w-auto" // Ensure consistent width
              >
                How It Works
              </Button>
            </Link>

            <Button
              size="lg"
              className="w-full sm:w-auto bg-primary text-primary-foreground hover:bg-primary/90" // Ensure consistent width
              onClick={() => setIsAuthModalOpen(true)}
            >
              Get Started
            </Button>
          </div>
        </div>
      </div>

      {/* Right side: ChatBox (only for desktop) */}
      {!isMobile && (
         <div className="w-full flex bg-gradient-to-b from-indigo-100 to-blue-50   px-6 py-14">
         {/* <div className="w-full bg-white max-w-xl rounded-lg shadow-lg   "> */}
           <ChatBox />
         {/* </div> */}
       </div>
      )}


      <AuthModal open={isAuthModalOpen} onClose={() => setIsAuthModalOpen(false)} />

    </section>
  );
}
