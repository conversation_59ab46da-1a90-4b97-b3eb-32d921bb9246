"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

interface OfferActivationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  offerId: string;
  offerTitle: string;
  offerBudget: number;
  isActive: boolean;
}

export default function OfferActivationDialog({
  isOpen,
  onClose,
  offerId,
  offerTitle,
  offerBudget,
  isActive,
}: OfferActivationDialogProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleToggleStatus = async () => {
    if (!user) return;

    // If deactivating, no need to check budget
    if (isActive) {
      await updateOfferStatus(false);
      return;
    }

    // Check if offer budget is greater than 0
    if (!offerBudget || offerBudget <= 0) {
      toast.error("Cannot activate offer with zero budget. Please edit the offer to set a budget.");
      return;
    }

    // If budget is valid, activate the offer
    await updateOfferStatus(true);
  };

  const updateOfferStatus = async (active: boolean) => {
    if (!user) return;
    setLoading(true);

    try {
      const token = await user.getIdToken();
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/${offerId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          active,
        }),
      });

      if (!res.ok) {
        throw new Error("Failed to update offer status");
      }

      toast.success(`Offer ${active ? "activated" : "paused"} successfully`);
      router.refresh();
      onClose();
    } catch (err) {
      console.error("Error updating offer status:", err);
      toast.error(`Failed to ${active ? "activate" : "pause"} offer`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isActive ? "Pause Offer" : "Activate Offer"}
          </DialogTitle>
          <DialogDescription>
            {isActive
              ? "This will pause your offer and it will no longer be shown to users."
              : "This will activate your offer and make it visible to users."}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <div className="rounded-lg bg-muted p-4 space-y-2">
            <h4 className="font-medium text-sm">Offer Details</h4>
            <p className="text-sm">{offerTitle}</p>
            <div className="flex justify-between text-sm">
              <span>Budget:</span>
              <span className="font-medium">{formatCurrency(offerBudget)}</span>
            </div>
            {!isActive && (
              <div className="mt-2">
                {/* Check if budget is zero */}
                {!offerBudget || offerBudget <= 0 ? (
                  <div className="flex items-start gap-2 text-red-500 text-sm">
                    <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <span>
                      Cannot activate offer with zero budget. Please edit the offer to set a budget.
                    </span>
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant={isActive ? "destructive" : "default"}
            onClick={handleToggleStatus}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isActive ? "Pausing..." : "Activating..."}
              </>
            ) : (
              isActive ? "Pause Offer" : "Activate Offer"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
