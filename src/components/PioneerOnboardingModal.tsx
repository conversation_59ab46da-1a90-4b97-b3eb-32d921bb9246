"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle, ChevronRight, Gift, Sparkles, Zap, Shield } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";

// Define profession options
const PROFESSION_OPTIONS = [
  "Student",
  "Engineer",
  "Founder",
  "Designer",
  "Doctor",
  "Freelancer",
  "Researcher",
  "Other"
];

// Define interest tag options
const INTEREST_OPTIONS = [
  "productivity",
  "finance",
  "ai tools",
  "writing",
  "health",
  "ecommerce",
  "marketing",
  "education",
  "design",
  "development"
];

export default function PioneerOnboardingModal({
  open,
  onClose
}: {
  open: boolean;
  onClose: () => void;
}) {
  const { user, xp } = useAuth();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  // Profile data
  const [agentName, setAgentName] = useState("");
  const [profession, setProfession] = useState("");
  const [otherProfession, setOtherProfession] = useState("");
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);

  // Handle step navigation
  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);

  // Handle interest selection
  const toggleInterest = (interest: string) => {
    if (selectedInterests.includes(interest)) {
      setSelectedInterests(selectedInterests.filter(i => i !== interest));
    } else {
      setSelectedInterests([...selectedInterests, interest]);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!user) return;

    setIsLoading(true);

    try {
      const token = await user.getIdToken();

      // Prepare profile data
      const profileData = {
        name: user.displayName || "",
        agentName: agentName.trim() || "AdMesh Assistant", // Default name if not provided
        profession: profession === "Other" ? otherProfession.trim() : profession,
        interests: selectedInterests,
        onboardingStatus: "completed"
      };

      // Update user profile
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/update-onboarding`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(profileData)
      });

      if (!response.ok) {
        throw new Error("Failed to update profile");
      }

      // Move to confirmation step
      nextStep();

    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to save your profile. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle completion
  const handleComplete = () => {
    onClose();
    // Force refresh to update the UI
    window.location.href = "/dashboard/user/chat";
  };

  // Handle dialog open/close
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Close the modal when dialog is closed
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        className="max-w-md sm:max-w-lg w-[95vw] p-0 rounded-xl bg-white dark:bg-gray-900 border shadow-xl max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogTitle className="sr-only">
          {step === 1 ? "Agent Pioneer Program" : step === 2 ? "Complete Your Profile" : "Onboarding Complete"}
        </DialogTitle>
        <AnimatePresence mode="wait">
          {step === 1 && (
            <motion.div
              key="welcome"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="p-6 sm:p-8 w-full pt-10"
            >
              <div className="text-center space-y-4 mb-8">
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                    <Sparkles className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Introducing the Agent Pioneer Program</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Become one of the first users to shape how AI agents discover, recommend, and reward across the AdMesh network.
                  Your early actions will train smarter agents and unlock exclusive benefits.
                </p>
              </div>

              <div className="space-y-4 mb-8">
                <div className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                  <Zap className="h-5 w-5 text-amber-500 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">50% more per successful recommendation</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Earn higher rewards for the same actions</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                  <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">Priority visibility on product suggestions</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Your feedback shapes what others see</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                  <Gift className="h-5 w-5 text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">Early access to premium offers</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Be the first to try exclusive products</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                  <Shield className="h-5 w-5 text-purple-500 dark:text-purple-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">Permanent Pioneer badge on your profile</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Show off your early adopter status</p>
                  </div>
                </div>
              </div>

              <Button
                onClick={nextStep}
                className="w-full h-12 rounded-lg bg-primary hover:bg-primary/90 text-primary-foreground flex items-center justify-center gap-2 mt-8"
              >
                Get Started
                <ChevronRight className="h-4 w-4" />
              </Button>

              {/* Add extra padding at the bottom to ensure content isn't hidden */}
              <div className="h-8"></div>
            </motion.div>
          )}

          {step === 2 && (
            <motion.div
              key="profile"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="p-6 sm:p-8 w-full pt-10"
            >
              <div className="text-center space-y-2 mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Complete Your Profile</h2>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Help us personalize your experience and recommendations
                </p>
              </div>

              <div className="space-y-6">


                {/* Agent Name field */}
                <div className="space-y-2">
                  <Label htmlFor="agentName" className="text-gray-900 dark:text-gray-100">Name Your Agent</Label>
                  <Input
                    id="agentName"
                    value={agentName}
                    onChange={(e) => setAgentName(e.target.value)}
                    placeholder="e.g. Shoppy, ProductGuru (optional)"
                    className="h-12"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    This name will appear in your chat window instead of &quot;AdMesh Assistant&quot;
                  </p>
                </div>

                {/* Profession selection */}
                <div className="space-y-3">
                  <Label className="text-gray-900 dark:text-gray-100">Who Are You?</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {PROFESSION_OPTIONS.map((option) => (
                      <button
                        key={option}
                        type="button"
                        onClick={() => setProfession(option)}
                        className={`p-3 rounded-lg border text-sm font-medium transition-all ${
                          profession === option
                            ? "border-primary bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground"
                            : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 text-gray-700 dark:text-gray-200"
                        }`}
                      >
                        {option}
                      </button>
                    ))}
                  </div>

                  {/* Other profession input */}
                  {profession === "Other" && (
                    <Input
                      value={otherProfession}
                      onChange={(e) => setOtherProfession(e.target.value)}
                      placeholder="Specify your profession"
                      className="mt-2 h-12"
                    />
                  )}
                </div>

                {/* Interest keywords */}
                <div className="space-y-3">
                  <Label className="text-gray-900 dark:text-gray-100">Interest Keywords (Select up to 5)</Label>
                  <div className="flex flex-wrap gap-2 max-w-full">
                    {INTEREST_OPTIONS.map((interest) => (
                      <button
                        key={interest}
                        type="button"
                        onClick={() => toggleInterest(interest)}
                        disabled={selectedInterests.length >= 5 && !selectedInterests.includes(interest)}
                        className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all ${
                          selectedInterests.includes(interest)
                            ? "bg-primary text-primary-foreground"
                            : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700"
                        } ${
                          selectedInterests.length >= 5 && !selectedInterests.includes(interest)
                            ? "opacity-50 cursor-not-allowed"
                            : ""
                        }`}
                      >
                        {interest}
                      </button>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Selected: {selectedInterests.length}/5
                  </p>
                </div>
              </div>

              <div className="flex gap-3 mt-8">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  className="flex-1 h-12"
                >
                  Back
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={isLoading || !profession || (profession === "Other" && !otherProfession.trim())}
                  className="flex-1 h-12 bg-primary hover:bg-primary/90 text-primary-foreground"
                >
                  {isLoading ? "Saving..." : "Continue"}
                </Button>
              </div>

              {/* Add extra padding at the bottom to ensure content isn't hidden */}
              <div className="h-8"></div>
            </motion.div>
          )}

          {step === 3 && (
            <motion.div
              key="confirmation"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="p-6 sm:p-8 w-full pt-10"
            >
              <div className="text-center space-y-4 mb-8">
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">You&apos;re all set!</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Welcome to the Agent Pioneer Program. You&apos;re now ready to start exploring and earning rewards.
                </p>
              </div>

              {/* XP Progress */}
              <div className="mb-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Pioneer Progress</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{xp} / 1000 XP</span>
                </div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-primary rounded-full"
                    style={{ width: `${Math.min(100, (xp / 1000) * 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  Earn XP through queries, follow-ups, and exploring tools
                </p>
              </div>

              <Button
                onClick={handleComplete}
                className="w-full h-12 rounded-lg bg-primary hover:bg-primary/90 text-primary-foreground mt-8"
              >
                Start Exploring
              </Button>

              {/* Add extra padding at the bottom to ensure content isn't hidden */}
              <div className="h-8"></div>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
