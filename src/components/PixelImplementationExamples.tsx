import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type SectionKey = 'html' | 'react' | 'nextjs' | 'gtm';

type Step = {
  title: string;
  description?: string;
  code?: string;
};

type Section = {
  label: string;
  steps: Step[];
};

type Sections = {
  [key in SectionKey]: Section;
};

export default function PixelImplementationExamples({ goal = "signup", productSlug = "YOUR_PRODUCT_SLUG" }: { goal?: string; productSlug?: string }) {
  const [tab, setTab] = useState<SectionKey>("html");
  const [currentStep, setCurrentStep] = useState<number>(0);
  // Track completed steps for each section
  const [, setCompletedSteps] = useState<Record<SectionKey, number[]>>({
    html: [],
    react: [],
    nextjs: [],
    gtm: []
  });

  const sections: Sections = {
    html: {
      label: "HTML Site",
      steps: [
        {
          title: "Step 1: Add this to your signup/landing page",
          description: `Add this script to your signup or landing page to store the tracking parameters.`,
          code: `<!-- AdMesh Tracking - Add to signup/landing page -->
<script>
  (function () {
    const params = new URLSearchParams(window.location.search);
    const clickId = params.get('utm_click_id');
    const source = params.get('utm_source');
    const target = params.get('utm_target'); // this is the product slug from AdMesh
    const test = params.get('test'); // get test parameter

    if (clickId && source === 'admesh' && target) {
      const key = \`admesh_referral_\${target}\`;
      localStorage.setItem(key, JSON.stringify({ clickId, source, test }));
    }
  })();
</script>`
        },
        {
          title: "Step 2: Add this to your thank-you/dashboard page",
          description: `Add this script to your thank-you or dashboard page to fire the conversion pixel.`,
          code: `<!-- AdMesh Conversion Pixel - Add to thank-you/dashboard page -->
<script>
  (function () {
    // Get the slug from the URL pathname
    const urlPath = window.location.pathname;
    const urlSlug = urlPath.split('/').filter(Boolean).pop() || "${productSlug}";
    const target = urlSlug; // Use the URL slug as the target
    const key = \`admesh_referral_\${target}\`;
    const data = localStorage.getItem(key);

    if (data) {
      try {
        const { clickId, source, test } = JSON.parse(data);
        if (clickId && source === 'admesh') {
          const img = document.createElement('img');
          img.src = \`https://api.useadmesh.com/conversion/pixel?utm_click_id=\${clickId}&test=\${test || 'false'}\`;
          img.width = 1;
          img.height = 1;
          img.style.display = 'none';
          document.body.appendChild(img);
          localStorage.removeItem(key); // prevent duplicate fire
          console.log('AdMesh conversion tracked:', clickId);
        }
      } catch (e) {
        console.error('AdMesh pixel error:', e);
      }
    }
  })();
</script>`
        }
      ]
    },
    react: {
      label: "React App",
      steps: [
        {
          title: "Step 1: Add to your SignupPage.jsx",
          description: `Add this to your signup or landing page component to store tracking parameters.`,
          code: `import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

export default function SignupPage() {
  const [searchParams] = useSearchParams();

  // Store AdMesh tracking parameters in localStorage
  useEffect(() => {
    const clickId = searchParams.get('utm_click_id');
    const source = searchParams.get('utm_source');
    const target = searchParams.get('utm_target');
    const test = searchParams.get('test'); // get test parameter

    if (clickId && source === 'admesh' && target) {
      const key = \`admesh_referral_\${target}\`;
      localStorage.setItem(key, JSON.stringify({ clickId, source, test }));
    }
  }, [searchParams]);

  return (
    <div>
      <h1>Sign up for ${goal}</h1>
      {/* Your signup form here */}
    </div>
  );
}`
        },
        {
          title: "Step 2: Add to your ThankYouPage.jsx",
          description: `Add this to your thank-you or dashboard page to fire the conversion pixel.`,
          code: `import { useEffect } from 'react';

export default function ThankYouPage() {
  // Fire AdMesh conversion pixel
  useEffect(() => {
    // Get the slug from the URL pathname
    const urlPath = window.location.pathname;
    const urlSlug = urlPath.split('/').filter(Boolean).pop() || "${productSlug}";
    const target = urlSlug; // Use the URL slug as the target
    const key = \`admesh_referral_\${target}\`;
    const data = localStorage.getItem(key);

    if (data) {
      try {
        const { clickId, source, test } = JSON.parse(data);
        if (clickId && source === 'admesh') {
          const img = document.createElement('img');
          img.src = \`https://api.useadmesh.com/conversion/pixel?utm_click_id=\${clickId}&test=\${test || 'false'}\`;
          img.width = 1;
          img.height = 1;
          img.style.display = 'none';
          document.body.appendChild(img);
          localStorage.removeItem(key); // prevent duplicate fire
          console.log('AdMesh conversion tracked:', clickId);
        }
      } catch (e) {
        console.error('AdMesh pixel error:', e);
      }
    }
  }, []);

  return <h1>Thank you for your ${goal}!</h1>;
}`
        }
      ]
    },
    nextjs: {
      label: "Next.js App",
      steps: [
        {
          title: "Step 1: Add to your signup page",
          description: `Add this to your signup or landing page component to store tracking parameters.`,
          code: `'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

export default function SignupPage() {
  const searchParams = useSearchParams();

  // Store AdMesh tracking parameters in localStorage
  useEffect(() => {
    const clickId = searchParams.get('utm_click_id');
    const source = searchParams.get('utm_source');
    const target = searchParams.get('utm_target');
    const test = searchParams.get('test'); // get test parameter

    if (clickId && source === 'admesh' && target) {
      const key = \`admesh_referral_\${target}\`;
      localStorage.setItem(key, JSON.stringify({ clickId, source, test }));
    }
  }, [searchParams]);

  return (
    <div>
      <h1>Sign up for ${goal}</h1>
      {/* Your signup form here */}
    </div>
  );
}`
        },
        {
          title: "Step 2: Add to your thank-you or dashboard page",
          description: `Add this to your thank-you or dashboard page to fire the conversion pixel.`,
          code: `'use client';

import { useEffect } from 'react';

export default function ThankYouPage() {
  // Fire AdMesh conversion pixel
  useEffect(() => {
    // Get the slug from the URL pathname
    const urlPath = window.location.pathname;
    const urlSlug = urlPath.split('/').filter(Boolean).pop() || "${productSlug}";
    const target = urlSlug; // Use the URL slug as the target
    const key = \`admesh_referral_\${target}\`;
    const data = localStorage.getItem(key);

    if (data) {
      try {
        const { clickId, source, test } = JSON.parse(data);
        if (clickId && source === 'admesh') {
          const img = document.createElement('img');
          img.src = \`https://api.useadmesh.com/conversion/pixel?utm_click_id=\${clickId}&test=\${test || 'false'}\`;
          img.width = 1;
          img.height = 1;
          img.style.display = 'none';
          document.body.appendChild(img);
          localStorage.removeItem(key); // prevent duplicate fire
          console.log('AdMesh conversion tracked:', clickId);
        }
      } catch (e) {
        console.error('AdMesh pixel error:', e);
      }
    }
  }, []);

  return <h1>Thank you for your ${goal}!</h1>;
}`
        }
      ]
    },
    gtm: {
      label: "Google Tag Manager",
      steps: [
        {
          title: "Step 1: Create a Tag for Landing Page",
          description: `In GTM, create a Custom HTML tag named "AdMesh Tracking - Landing Page" with this code:`,
          code: `<script>
(function () {
  var params = new URLSearchParams(window.location.search);
  var clickId = params.get('utm_click_id');
  var source = params.get('utm_source');
  var target = params.get('utm_target');
  var test = params.get('test'); // get test parameter

  if (clickId && source === 'admesh' && target) {
    var key = 'admesh_referral_' + target;
    localStorage.setItem(key, JSON.stringify({ clickId: clickId, source: source, test: test }));
    console.log("AdMesh tracking stored for:", target);
  }
})();
</script>`
        },
        {
          title: "Step 2: Create a Tag for Conversion Page",
          description: `Create another Custom HTML tag named "AdMesh Conversion Pixel" with this code:`,
          code: `<script>
(function () {
  // Get the slug from the URL pathname
  var urlPath = window.location.pathname;
  var urlSlug = urlPath.split('/').filter(Boolean).pop() || "${productSlug}";
  var target = urlSlug; // Use the URL slug as the target
  var key = 'admesh_referral_' + target;
  var data = localStorage.getItem(key);

  if (data) {
    try {
      var parsed = JSON.parse(data);
      var clickId = parsed.clickId;
      var source = parsed.source;
      var test = parsed.test;

      if (clickId && source === 'admesh') {
        var img = document.createElement('img');
        img.src = "https://api.useadmesh.com/conversion/pixel?utm_click_id=" + clickId + "&test=" + (test || 'false');
        img.width = 1;
        img.height = 1;
        img.style.display = 'none';
        document.body.appendChild(img);
        localStorage.removeItem(key); // prevent duplicate fire
        console.log("AdMesh conversion tracked:", clickId);
      }
    } catch (e) {
      console.error('AdMesh pixel error:', e);
    }
  }
})();
</script>`
        },
        {
          title: "Step 3: Set Up Triggers",
          description: `For the landing page tag, create a trigger that fires on your signup page.
For the conversion pixel tag, create a trigger that fires on your thank-you or dashboard page.
Use "Page URL contains /signup" for the first tag and "Page URL contains /dashboard" for the second.`
        },
        {
          title: "Step 4: Publish the Container",
          description: `Click "Submit" and publish your GTM container. Test the flow by visiting your signup page with
"?utm_click_id=test123&utm_source=admesh&utm_target=${productSlug}" and then completing the signup process.`
        }
      ]
    }
  };

  // Function to handle step progression
  const handleNextStep = (sectionKey: SectionKey) => {
    const maxSteps = sections[sectionKey].steps.length;
    if (currentStep < maxSteps - 1) {
      setCurrentStep(currentStep + 1);
    }

    // Mark current step as completed
    setCompletedSteps(prev => ({
      ...prev,
      [sectionKey]: [...prev[sectionKey], currentStep].filter((v, i, a) => a.indexOf(v) === i)
    }));
  };

  // We're not using these functions in the current implementation, but keeping them
  // for future reference or if we need to re-enable this functionality later
  /*
  // Function to check if a step is completed
  const isStepCompleted = (sectionKey: SectionKey, stepIndex: number) => {
    return completedSteps[sectionKey].includes(stepIndex);
  };

  // Function to toggle step visibility
  const toggleStep = (stepIndex: number) => {
    if (stepIndex <= currentStep) {
      // If clicking on a previous step, navigate to it
      setCurrentStep(stepIndex);
    }
  };
  */

  return (
    <div className="border border-border rounded-lg overflow-hidden">
      <div className="p-4 border-b">
        <h4 className="text-xl font-medium mb-2">Implementation Steps</h4>
        <p className="text-sm text-muted-foreground mb-4">
          Follow these steps to implement the tracking code on your website.
        </p>

        <div className="mb-4">
          <Select value={tab} onValueChange={(value) => {
            setTab(value as SectionKey);
            setCurrentStep(0); // Reset to first step when changing implementation
          }}>
            <SelectTrigger className="w-full md:w-[250px]">
              <SelectValue placeholder="Select implementation" />
            </SelectTrigger>
            <SelectContent>
              {(Object.entries(sections) as [SectionKey, Section][]).map(([key, section]) => (
                <SelectItem key={key} value={key}>{section.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {(Object.entries(sections) as [SectionKey, Section][]).map(([key, section]) => (
          key === tab && (
            <div key={key}>
              <Accordion
                type="single"
                collapsible
                defaultValue={`step-${currentStep}`}
                className="space-y-4"
              >
                {section.steps.map((step, i) => (
                  <AccordionItem
                    key={i}
                    value={`step-${i}`}
                    className={`
                      border rounded-md transition-all duration-300 overflow-hidden
                      ${i === currentStep
                        ? 'border-primary/30 bg-primary/5 shadow-md'
                        : i < currentStep
                          ? 'border-gray-200 bg-gray-50'
                          : 'border-gray-200 bg-gray-50 opacity-70'}
                    `}
                  >
                    <AccordionTrigger
                      className="px-4 py-3 hover:no-underline hover:bg-muted/20 transition-colors"
                      onClick={() => {
                        if (i <= section.steps.length - 1) {
                          setCurrentStep(i);
                        }
                      }}
                    >
                      <div className="flex items-center">
                        <span className={`
                          inline-flex items-center justify-center w-6 h-6 mr-3 text-xs rounded-full
                          ${i === currentStep
                            ? 'bg-primary text-white'
                            : i < currentStep
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-300 text-gray-600'}
                        `}>
                          {i < currentStep ? <Check className="w-3 h-3" /> : i + 1}
                        </span>
                        <h5 className="font-medium text-sm">{step.title}</h5>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4">
                      <div className="pl-9">
                        {step.description && (
                          <p className="text-sm text-muted-foreground mb-3">{step.description}</p>
                        )}

                        {step.code && (
                          <div className="bg-white border rounded-md p-3 text-xs font-mono overflow-auto">
                            <pre>{step.code}</pre>
                            <div className="flex items-center mt-2 gap-2">
                              <Button
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (step.code) {
                                    navigator.clipboard.writeText(step.code);
                                    const button = e.currentTarget;
                                    if (button) {
                                      const originalText = button.textContent;
                                      button.textContent = "Copied!";
                                      setTimeout(() => {
                                        button.textContent = originalText;
                                      }, 1000);
                                    }
                                  }
                                }}
                              >
                                Copy Code
                              </Button>

                              {i < section.steps.length - 1 && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="ml-auto"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleNextStep(key);
                                    // Open the next accordion item
                                    document.querySelector(`[data-state="closed"][value="step-${i+1}"]`)?.dispatchEvent(
                                      new MouseEvent('click', { bubbles: true })
                                    );
                                  }}
                                >
                                  Next Step
                                </Button>
                              )}
                            </div>
                          </div>
                        )}

                        {/* For steps without code but with Next button */}
                        {!step.code && i < section.steps.length - 1 && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="mt-2"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleNextStep(key);
                              // Open the next accordion item
                              document.querySelector(`[data-state="closed"][value="step-${i+1}"]`)?.dispatchEvent(
                                new MouseEvent('click', { bubbles: true })
                              );
                            }}
                          >
                            Next Step
                          </Button>
                        )}

                        {/* Completion message for the last step */}
                        {i === section.steps.length - 1 && (
                          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md text-green-800">
                            <p className="text-sm font-medium flex items-center">
                              <Check className="w-4 h-4 mr-2" />
                              All steps completed! Your tracking is now set up.
                            </p>
                          </div>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          )
        ))}
      </div>
    </div>
  );
}
