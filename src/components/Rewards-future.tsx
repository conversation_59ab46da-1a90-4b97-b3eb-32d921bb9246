"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Gift, Coins, ChevronRight, <PERSON>, Crown } from "lucide-react";
import { motion } from "framer-motion";
import AuthModal from "@/components/AuthModal";

export default function Rewards() {
  const [authOpen, setAuthOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("earn");

  // Sample data for recent rewards
  const recentRewards = [
    { action: "Asked a question", credits: 15, time: "2 min ago" },
    { action: "Tried Search tool", credits: 25, time: "Yesterday" },
    { action: "Daily login bonus", credits: 50, time: "Today" },
  ];

  return (
    <>
      <section
        id="rewards"
        className="py-32 bg-gradient-to-b from-indigo-50 via-blue-50 to-white px-6 text-center relative overflow-hidden"
      >
        {/* Decorative background elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute -top-20 -left-20 w-64 h-64 bg-indigo-100 rounded-full opacity-60 blur-3xl"></div>
          <div className="absolute top-40 -right-20 w-80 h-80 bg-blue-100 rounded-full opacity-60 blur-3xl"></div>
        </div>

        <div className="container mx-auto max-w-6xl relative z-10">
          {/* Section Header with animated gradient text */}
          <div className="mb-16">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="inline-block mb-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium"
            >
              AdMesh Rewards Program
            </motion.div>
            
            <h2 className="text-4xl sm:text-5xl font-extrabold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600 mb-6">
              🎁 Earn While You Explore
            </h2>
            
            <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
              Every interaction builds your AI agent and rewards you with AdMesh credits.
              Convert these credits to real value as marketplace offers go live.
            </p>
          </div>

          {/* Tabs Navigation */}
          <div className="flex justify-center mb-12">
            <div className="inline-flex bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveTab("earn")}
                className={`px-5 py-2 rounded-md text-sm font-medium transition ${
                  activeTab === "earn" 
                    ? "bg-white text-indigo-600 shadow-sm" 
                    : "text-gray-600 hover:text-indigo-600"
                }`}
              >
                Earn
              </button>
              <button
                onClick={() => setActiveTab("stats")}
                className={`px-5 py-2 rounded-md text-sm font-medium transition ${
                  activeTab === "stats" 
                    ? "bg-white text-indigo-600 shadow-sm" 
                    : "text-gray-600 hover:text-indigo-600"
                }`}
              >
                Your Stats
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="transition-all duration-300">
            {/* Earn Tab */}
            {activeTab === "earn" && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4 }}
              >
                {/* Reward Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                  <RewardCard
                    icon={<Sparkles className="w-6 h-6 text-indigo-500" />}
                    title="Ask Questions"
                    description="Every query earns you credits while helping our AI get smarter."
                    amount="15-30 credits"
                  />
                  <RewardCard
                    icon={<Coins className="w-6 h-6 text-yellow-500" />}
                    title="Try Tools"
                    description="Clicking, comparing, or sharing tools gives you more rewards."
                    amount="25-75 credits"
                  />
                  <RewardCard
                    icon={<Gift className="w-6 h-6 text-pink-500" />}
                    title="Refer Friends"
                    description="Earn bonus credits when friends join using your link."
                    amount="200 credits per referral"
                  />
                </div>

                {/* Bonus Tiers */}
                <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm max-w-2xl mx-auto mb-16">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Crown className="w-5 h-5 mr-2 text-amber-500" />
                    Loyalty Tiers
                  </h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="p-3 rounded-lg bg-gray-50 text-center">
                      <div className="font-medium text-gray-800">Bronze</div>
                      <div className="text-xs text-gray-500">1x Multiplier</div>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-50 text-center">
                      <div className="font-medium text-gray-800">Silver</div>
                      <div className="text-xs text-gray-500">1.5x Multiplier</div>
                    </div>
                    <div className="p-3 rounded-lg bg-indigo-50 border border-indigo-100 text-center">
                      <div className="font-medium text-indigo-600">Gold</div>
                      <div className="text-xs text-indigo-500">2x Multiplier</div>
                    </div>
                  </div>
                </div>

                {/* Bonus Tip */}
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg inline-block text-left mx-auto">
                  <p className="text-sm text-gray-700">
                    💡 <strong>Tip:</strong> You start with <strong>500 free credits</strong> just for signing up. Every credit spent is added to your <strong>reward balance</strong> — withdrawable as cash 💸 later.
                  </p>
                </div>
              </motion.div>
            )}

            {/* Stats Tab */}
            {activeTab === "stats" && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4 }}
                className="max-w-2xl mx-auto"
              >
                {/* Credit Balance Card */}
                <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl p-6 text-white shadow-lg mb-8">
                  <h3 className="text-lg font-medium opacity-90 mb-1">Total Credits Earned</h3>
                  <p className="text-4xl font-bold mb-4">1,250</p>
                  <div className="flex justify-between text-sm">
                    <span className="opacity-90">Current tier: Silver</span>
                    <span className="opacity-90">Next tier: 750 more credits</span>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 mb-8">
                  <h3 className="font-medium flex items-center text-gray-800 mb-4">
                    <Clock className="w-4 h-4 mr-2 text-gray-500" />
                    Recent Activity
                  </h3>
                  <div className="space-y-3">
                    {recentRewards.map((reward, index) => (
                      <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-0">
                        <div className="flex items-center">
                          <div className="bg-indigo-100 p-1 rounded-full mr-3">
                            <Coins className="w-4 h-4 text-indigo-600" />
                          </div>
                          <div className="text-sm">
                            <p className="text-gray-800">{reward.action}</p>
                            <p className="text-gray-500 text-xs">{reward.time}</p>
                          </div>
                        </div>
                        <div className="text-sm font-medium text-indigo-600">+{reward.credits}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Scroll-animated CTA */}
          <motion.div
            className="mt-10"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            viewport={{ once: true, amount: 0.6 }}
          >
            <button
              onClick={() => setAuthOpen(true)}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium py-3 px-8 rounded-lg shadow-md transition flex items-center mx-auto"
            >
              Start Earning Now
              <ChevronRight className="w-4 h-4 ml-2" />
            </button>
          </motion.div>
        </div>
      </section>

      {/* Auth Modal */}
      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} />
    </>
  );
}

function RewardCard({
  icon,
  title,
  description,
  amount,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  amount: string;
}) {
  return (
    <motion.div 
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
      className="border border-gray-200 rounded-2xl bg-white p-6 text-left shadow-sm hover:shadow-md transition"
    >
      <div className="flex items-center gap-3 mb-4">
        <div className="rounded-full bg-indigo-50 p-2">{icon}</div>
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      <p className="text-sm text-gray-600 mb-4">{description}</p>
      <div className="text-sm font-medium text-indigo-600 bg-indigo-50 py-1 px-3 rounded-full inline-block">
        {amount}
      </div>
    </motion.div>
  );
}