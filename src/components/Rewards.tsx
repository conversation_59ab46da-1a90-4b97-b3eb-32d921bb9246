"use client";

import { useState, useEffect } from "react";
import { Sparkles, Gift, Coins } from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { auth } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";
import AuthModal from "@/components/AuthModal";

export default function Rewards() {
  const [authOpen, setAuthOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const unsub = onAuthStateChanged(auth, (user) => {
      if (user && !user.isAnonymous) {
        router.push("/dashboard");
      }
    });
    return () => unsub();
  }, [router]);

  return (
    <>
      <section
        id="rewards"
        className="py-32 bg-gradient-to-b from-blue-50 to-white px-6 text-center"
      >
        <div className="container mx-auto max-w-6xl">
          {/* Section Header */}
          <h2 className="text-4xl sm:text-5xl font-extrabold tracking-tight text-gray-900 mb-6">
            🎁 <PERSON>arn Rewards as You Explore
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto mb-16">
            Every query, click, and follow-up helps shape your AI agent — and earns you AdMesh credits.
            These credits convert into wallet value as real offers go live.
          </p>

          {/* Reward Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <RewardCard
              icon={<Sparkles className="w-6 h-6 text-indigo-500" />}
              title="Ask Questions"
              description="Every query earns you credits while helping our AI get smarter."
            />
            <RewardCard
              icon={<Coins className="w-6 h-6 text-yellow-500" />}
              title="Try Tools"
              description="Clicking, comparing, or sharing tools gives you more rewards."
            />
            <RewardCard
              icon={<Gift className="w-6 h-6 text-pink-500" />}
              title="Unlock Perks"
              description="Early users get priority access, bonus multipliers, and lifetime badges."
            />
          </div>

          {/* Bonus Tip */}
          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg inline-block text-left mx-auto mt-8">
                  <p className="text-sm text-gray-700">
                    💡 <strong>Tip:</strong> You start with <strong>500 free credits</strong> just for signing up. Every credit spent is added to your <strong>reward balance</strong> — withdrawable as cash 💸 later.
                  </p>
                </div>


          {/* Scroll-animated CTA */}
          <motion.div
            className="mt-10"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            viewport={{ once: true, amount: 0.6 }}
          >
            <button
              onClick={() => {
                if (auth.currentUser && !auth.currentUser.isAnonymous) {
                  router.push("/dashboard");
                } else {
                  setAuthOpen(true);
                }
              }}
              className="bg-indigo-600 hover:bg-indigo-700 text-white text-sm sm:text-base font-medium py-3 px-6 rounded-lg shadow transition"
            >
              Try AdMesh
            </button>
          </motion.div>
        </div>
      </section>

      {/* Auth Modal */}
      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} />
    </>
  );
}

function RewardCard({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="border border-border rounded-2xl bg-white p-6 text-left shadow-sm hover:shadow-md transition">
      <div className="flex items-center gap-3 mb-4">
        <div className="rounded-full bg-muted p-2">{icon}</div>
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
}
