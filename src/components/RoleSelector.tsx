"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";

type Role = "user" | "brand" | "agent" | null;

interface RoleSelectorProps {
  onRoleSelect: (role: Role) => void;
}

export default function RoleSelector({ onRoleSelect }: RoleSelectorProps) {
  const [hoveredRole, setHoveredRole] = useState<Role>(null);

  const roleOptions = [
    {
      id: "user",
      icon: "👤",
      label: "I am a User",
      description: "Train your personal AI agent and earn rewards",
    },
    {
      id: "brand",
      icon: "🏢",
      label: "I am a Brand",
      description: "Launch your offers into the agent economy",
    },
    {
      id: "agent",
      icon: "🤖",
      label: "I am an Agent",
      description: "Monetize your AI agent with AdMesh Protocol",
    },
  ];

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-indigo-50 to-blue-50 px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight leading-tight mb-6">
          How can we help you?
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Select your role to discover how AdMesh can help you
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl w-full">
        {roleOptions.map((role) => (
          <motion.div
            key={role.id}
            className={`relative overflow-hidden rounded-xl border bg-background p-6 shadow-sm transition-all duration-300 hover:shadow-md ${
              hoveredRole === role.id ? "ring-2 ring-primary" : ""
            }`}
            whileHover={{ y: -5 }}
            onMouseEnter={() => setHoveredRole(role.id as Role)}
            onMouseLeave={() => setHoveredRole(null)}
          >
            <div className="text-4xl mb-4">{role.icon}</div>
            <h3 className="text-xl font-semibold mb-2">{role.label}</h3>
            <p className="text-muted-foreground mb-6">{role.description}</p>
            <Button
              className="w-full"
              size="lg"
              onClick={() => onRoleSelect(role.id as Role)}
            >
              Continue as {role.id.charAt(0).toUpperCase() + role.id.slice(1)}
            </Button>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
