"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import BadgeIcon, { BadgeData } from "@/components/BadgeIcon";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface UserBadgesProps {
  size?: "sm" | "md" | "lg";
  showTooltip?: boolean;
  maxDisplay?: number;
  showRefresh?: boolean;
  showShareButton?: boolean;
  className?: string;
}

export default function UserBadges({
  size = "md",
  showTooltip = true,
  maxDisplay,
  showRefresh = false,
  showShareButton = false,
  className
}: UserBadgesProps) {
  const { user } = useAuth();
  const [badges, setBadges] = useState<BadgeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchBadges = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/badges/user/all`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to fetch badges");
      }

      const data = await response.json();
      setBadges(data.badges || []);
    } catch (error) {
      console.error("Error fetching badges:", error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  const checkForNewBadges = async () => {
    if (!user) return;

    try {
      setRefreshing(true);
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/badges/user/check`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to check for new badges");
      }

      const data = await response.json();

      if (data.count > 0) {
        toast.success(`You earned ${data.count} new badge${data.count > 1 ? 's' : ''}!`);
        fetchBadges(); // Refresh badges
      } else {
        toast.info("No new badges earned yet. Keep exploring!");
      }
    } catch (error) {
      console.error("Error checking for badges:", error);
      toast.error("Failed to check for new badges");
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchBadges();
  }, [user, fetchBadges]);

  if (loading) {
    return (
      <div className="flex gap-2">
        {[...Array(3)].map((_, i) => (
          <Skeleton key={i} className={`rounded-full ${size === 'sm' ? 'h-6 w-6' : size === 'lg' ? 'h-10 w-10' : 'h-8 w-8'}`} />
        ))}
      </div>
    );
  }

  if (badges.length === 0) {
    return null;
  }

  // Filter badges that should be displayed
  const displayBadges = badges.filter(badge => badge.is_displayed !== false);

  // Limit the number of badges to display if maxDisplay is provided
  const limitedBadges = maxDisplay ? displayBadges.slice(0, maxDisplay) : displayBadges;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {limitedBadges.map(badge => (
        <BadgeIcon
          key={badge.badge_id}
          badge={badge}
          size={size}
          showTooltip={showTooltip}
          showShareButton={showShareButton}
        />
      ))}

      {maxDisplay && displayBadges.length > maxDisplay && (
        <div className={`rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 flex items-center justify-center
          ${size === 'sm' ? 'h-6 w-6 text-xs' : size === 'lg' ? 'h-10 w-10 text-sm' : 'h-8 w-8 text-xs'}`}>
          +{displayBadges.length - maxDisplay}
        </div>
      )}

      {showRefresh && (
        <Button
          variant="ghost"
          size="icon"
          className={`rounded-full ${size === 'sm' ? 'h-6 w-6' : size === 'lg' ? 'h-10 w-10' : 'h-8 w-8'}`}
          onClick={checkForNewBadges}
          disabled={refreshing}
        >
          <RefreshCw className={`${size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'} ${refreshing ? 'animate-spin' : ''}`} />
        </Button>
      )}
    </div>
  );
}
