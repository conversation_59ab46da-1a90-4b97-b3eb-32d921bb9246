"use client";

import { Check, X } from "lucide-react";
import { motion, useAnimation } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useEffect } from "react";

const fadeInSlow = {
  hidden: { opacity: 0, y: 50, scale: 0.95 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 1.2,
      ease: [0.25, 0.8, 0.25, 1], // smooth easeOut
    },
  },
};

const AnimatedCard = ({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.2 });
  const controls = useAnimation();

  useEffect(() => {
    if (inView) controls.start("visible");
  }, [inView, controls]);

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={fadeInSlow}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default function WhatMakesAdMesh() {
  return (
    <section
      id="what-is-admesh"
      className="w-full py-28 bg-gradient-to-b from-indigo-50 via-blue-100 to-indigo-200"
    >
      <div className="container mx-auto px-4 sm:px-6">
        {/* Header */}
        <AnimatedCard className="max-w-3xl mx-auto text-center mb-20">
          <h2 className="text-4xl sm:text-5xl font-extrabold tracking-tight text-primary mb-6">
            What Makes <span className="text-indigo-600">AdMesh</span> Different?
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            AdMesh is an open monetization protocol for agents, extensions, and AI tools.
            It connects user intent to structured offers — and shares verified earnings
            between agents, users, and the protocol. Built for fairness and performance.
          </p>
        </AnimatedCard>

        {/* Comparison Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-5xl mx-auto">
          {/* Traditional Ads */}
          <AnimatedCard className="group relative overflow-hidden border border-border rounded-xl p-8 backdrop-blur-sm bg-white/50 hover:shadow-2xl transition-all duration-300">
            <div className="absolute -top-20 -right-20 w-60 h-60 bg-red-200/30 rounded-full blur-2xl group-hover:opacity-70 transition" />
            <h3 className="text-xl font-semibold text-red-600 mb-6">Traditional Ads</h3>
            <ul className="space-y-4">
              {[
                "Pay-per-impression (even if ignored)",
                "No context or timing",
                "Spammy, intrusive placement",
                "No rewards for users or tools",
                "High fraud, low transparency",
              ].map((item, i) => (
                <motion.li
                  key={i}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{
                    duration: 0.6,
                    delay: i * 0.15,
                    ease: "easeOut",
                  }}
                  viewport={{ once: true, amount: 0.2 }}
                  className="flex items-start gap-3"
                >
                  <div className="mt-1 h-6 w-6 shrink-0 rounded-full bg-red-300 flex items-center justify-center">
                    <X size={14} className="text-red-600" />
                  </div>
                  <span className="text-muted-foreground">{item}</span>
                </motion.li>
              ))}
            </ul>
          </AnimatedCard>

          {/* AdMesh Protocol */}
          <AnimatedCard className="group relative overflow-hidden border border-border rounded-xl p-8 backdrop-blur-sm bg-white/50 hover:shadow-2xl transition-all duration-300">
            <div className="absolute -top-20 -right-20 w-60 h-60 bg-indigo-300/30 rounded-full blur-2xl group-hover:opacity-70 transition" />
            <h3 className="text-xl font-semibold text-indigo-600 mb-6">AdMesh Protocol</h3>
            <ul className="space-y-4">
              {[
                "Only pay on verified outcomes",
                "Intent-based and agent-triggered",
                "Built into trusted tools, GPTs, extensions",
                "Fair reward splits to agents & users",
                "Fraud protection + trust scoring",
              ].map((item, i) => (
                <motion.li
                  key={i}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{
                    duration: 0.6,
                    delay: i * 0.15,
                    ease: "easeOut",
                  }}
                  viewport={{ once: true, amount: 0.2 }}
                  className="flex items-start gap-3"
                >
                  <div className="mt-1 h-6 w-6 shrink-0 rounded-full bg-indigo-300 flex items-center justify-center">
                    <Check size={14} className="text-indigo-600" />
                  </div>
                  <span className="text-muted-foreground">{item}</span>
                </motion.li>
              ))}
            </ul>
          </AnimatedCard>
        </div>
      </div>
    </section>
  );
}
