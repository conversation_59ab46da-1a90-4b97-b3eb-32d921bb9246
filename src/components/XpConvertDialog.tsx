"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useCreditsContext } from "@/contexts/credits-context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, <PERSON>rkles, Coins, ArrowRight } from "lucide-react";
import { toast } from "sonner";

interface XpConvertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function XpConvertDialog({
  isOpen,
  onClose,
  onSuccess
}: XpConvertDialogProps) {
  const { user } = useAuth();
  const { credits, xp } = useCreditsContext();
  const [amount, setAmount] = useState("100");
  const [loading, setLoading] = useState(false);
  const [creditsToReceive, setCreditsToReceive] = useState(10);

  // Calculate credits to receive when amount changes
  useEffect(() => {
    const xpAmount = parseInt(amount) || 0;
    setCreditsToReceive(Math.floor(xpAmount / 10));
  }, [amount]);

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setAmount("100");
      setLoading(false);
    }
  }, [isOpen]);

  // Handle amount change
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers
    if (/^\d*$/.test(value)) {
      setAmount(value);
    }
  };

  // Handle conversion
  const handleConvert = async () => {
    if (!user) return;

    const xpAmount = parseInt(amount);

    // Validate amount
    if (!xpAmount || isNaN(xpAmount)) {
      toast.error("Please enter a valid amount");
      return;
    }

    if (xpAmount <= 0) {
      toast.error("Amount must be greater than zero");
      return;
    }

    if (xpAmount % 10 !== 0) {
      toast.error("Amount must be a multiple of 10");
      return;
    }

    if (xp === null || xpAmount > xp) {
      toast.error("You don't have enough XP");
      return;
    }

    try {
      setLoading(true);
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/pioneer/convert-xp`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ amount: xpAmount })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to convert XP");
      }

      const data = await response.json();

      // Show success toast
      toast.success(`Successfully converted ${xpAmount} XP to ${creditsToReceive} credits!`, {
        description: `Your new balance: ${data.new_xp} XP, ${data.new_credits} credits`,
        icon: <Coins className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
      });

      // Close dialog
      onClose();

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error converting XP:", error);
      toast.error(error instanceof Error ? error.message : "Failed to convert XP");
    } finally {
      setLoading(false);
    }
  };

  // Calculate if amount is valid
  const xpAmount = parseInt(amount) || 0;
  const isValid = xpAmount > 0 && xpAmount % 10 === 0 && xp !== null && xpAmount <= xp;
  const errorMessage = !xpAmount ? "Please enter an amount" :
                      xpAmount <= 0 ? "Amount must be greater than zero" :
                      xpAmount % 10 !== 0 ? "Amount must be a multiple of 10" :
                      xp === null || xpAmount > xp ? "You don't have enough XP" : "";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
            <span>Convert XP to <span className="text-green-500 dark:text-green-400">Credits</span></span>
          </DialogTitle>
          <DialogDescription>
            Exchange your XP for credits to use on the platform.
          </DialogDescription>
        </DialogHeader>

        {/* Current Balance */}
        <div className="flex justify-between items-center px-4 py-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
          <div>
            <div className="text-xs text-gray-500 mb-1">Current XP Balance</div>
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
              <span className="font-medium text-lg">{xp !== null ? xp : 0} XP</span>
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current Credits</div>
            <div className="flex items-center gap-2">
              <Coins className="h-4 w-4 text-green-500 dark:text-green-400" />
              <span className="font-medium text-lg text-green-500 dark:text-green-400">{credits !== null ? credits : '--'} Credits</span>
            </div>
          </div>
        </div>

        {/* Conversion Rate Card */}
        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-800">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Conversion Rate</h3>
          <div className="flex items-center justify-center gap-3">
            <div className="flex items-center gap-1 text-lg font-bold">
              <Sparkles className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
              <span>10 XP</span>
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            <div className="flex items-center gap-1 text-lg font-bold">
              <Coins className="h-5 w-5 text-green-500 dark:text-green-400" />
              <span className="text-green-500 dark:text-green-400">1 Credit</span>
            </div>
          </div>
        </div>

        <div className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="xp-amount" className="flex justify-between">
              <span>XP Amount</span>
              <span className="text-xs text-gray-500">Must be a multiple of 10</span>
            </Label>
            <Input
              id="xp-amount"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              value={amount}
              onChange={handleAmountChange}
              disabled={loading}
              className={!isValid && amount ? "border-red-300 focus-visible:ring-red-400" : ""}
              placeholder="Enter XP amount"
            />
            {errorMessage && amount && (
              <p className="text-sm text-red-500 mt-1">{errorMessage}</p>
            )}

            {/* Quick action buttons */}
            <div className="flex flex-wrap gap-2 mt-2">
              <p className="text-xs text-gray-500 w-full">Quick convert:</p>
              {[100, 500, 1000].map((quickAmount) => (
                <Button
                  key={quickAmount}
                  type="button"
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  disabled={loading || xp === null || quickAmount > xp}
                  onClick={() => setAmount(quickAmount.toString())}
                >
                  {quickAmount} XP
                </Button>
              ))}
              {xp !== null && xp >= 10 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  disabled={loading}
                  onClick={() => setAmount(Math.floor(xp / 10) * 10 + "")}
                >
                  Max
                </Button>
              )}
            </div>
          </div>

          {/* Conversion Preview */}
          <div className="flex flex-col gap-2 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Conversion Preview</h3>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Sparkles className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
                <span className="font-medium">{amount || 0} XP</span>
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400 dark:text-gray-500" />
              <div className="flex items-center gap-1">
                <Coins className="h-4 w-4 text-green-500 dark:text-green-400" />
                <span className="font-medium text-green-500 dark:text-green-400">{creditsToReceive} Credits</span>
              </div>
            </div>

            {/* Progress bar to show how much XP can be converted */}
            {xp !== null && xp > 0 && (
              <div className="w-full mt-2">
                <div className="flex justify-between text-xs mb-1">
                  <span>0 XP</span>
                  <span>{xp} XP</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${isValid ? 'bg-green-500 dark:bg-green-400' : 'bg-yellow-500 dark:bg-yellow-400'}`}
                    style={{ width: `${Math.min(100, (xpAmount / xp) * 100)}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose} disabled={loading} className="flex-1">
            Cancel
          </Button>
          <Button
            onClick={handleConvert}
            disabled={loading || !isValid}
            className="flex-1"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Converting...
              </>
            ) : (
              <>
                <Coins className="mr-2 h-4 w-4 text-green-500 dark:text-green-400" />
                Convert XP to <span className="text-green-500 dark:text-green-400">Credits</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
