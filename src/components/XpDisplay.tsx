"use client";

import { useAuth } from "@/hooks/use-auth";
import { useCreditsContext } from "@/contexts/credits-context";
import { Sparkles } from "lucide-react";
import Link from "next/link";

export default function XpDisplay() {
  const { role } = useAuth();
  const { xp } = useCreditsContext();

  // Only show for users
  if (role !== "user") return null;

  return (
    <Link href="/dashboard/user/badges" className="flex items-center gap-1 text-xs font-medium hover:text-primary transition-colors">
      <Sparkles className="h-3 w-3 text-yellow-500" />
      <span>{xp !== null ? xp : 0} XP</span>
    </Link>
  );
}
