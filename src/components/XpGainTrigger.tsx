"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { toast } from "sonner";
import { Sparkles } from "lucide-react";

interface XpGainTriggerProps {
  action: "query" | "followup" | "click" | "conversion";
  autoTrigger?: boolean;
  children?: React.ReactNode;
}

export default function XpGainTrigger({
  // action parameter is used for tracking different types of XP gain events
  action,
  autoTrigger = false,
  children
}: XpGainTriggerProps) {
  const { user, role } = useAuth();
  const [hasTriggered, setHasTriggered] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);

  // Trigger XP gain
  const triggerXpGain = useCallback(async () => {
    // Don't award XP for followups
    if (!user || role !== "user" || hasTriggered || action === "followup") return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/pioneer/increment-xp`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json"
        }
      });

      if (response.ok) {
        const data = await response.json();
        setHasTriggered(true);

        // Show XP gain animation
        setShowAnimation(true);
        setTimeout(() => setShowAnimation(false), 3000);

        // Show toast notification
        toast.success(`+${data.xpGained} XP gained!`, {
          description: "Keep exploring to earn more rewards",
          icon: <Sparkles className="h-4 w-4 text-yellow-500" />
        });
      } else {
        console.error("Error incrementing XP:", await response.text());
      }
    } catch (error) {
      console.error("Error incrementing XP:", error);
    }
  }, [user, role, hasTriggered, setHasTriggered, setShowAnimation, action]);

  // Auto-trigger XP gain if enabled
  useEffect(() => {
    if (autoTrigger && !hasTriggered) {
      triggerXpGain();
    }
  }, [autoTrigger, hasTriggered, triggerXpGain]);

  // If no children provided, return null (invisible component)
  if (!children) return null;

  // Wrap children with onClick handler to trigger XP gain
  return (
    <div onClick={triggerXpGain} className="relative">
      {children}

      {/* XP Gain Animation - only show for non-followup actions */}
      {showAnimation && action !== "followup" && (
        <div className="absolute top-0 right-0 pointer-events-none">
          <div className="flex items-center gap-1 bg-black text-white px-2 py-1 rounded-full text-sm font-medium animate-float">
            <Sparkles className="h-3 w-3 text-yellow-400" />
            +10 XP
          </div>
        </div>
      )}
    </div>
  );
}
