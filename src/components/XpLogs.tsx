"use client";

import { useAuth } from "@/hooks/use-auth";
import { useEffect, useState, useCallback } from "react";
import { <PERSON>rk<PERSON>, Co<PERSON>, ArrowRight, ChevronLeft, ChevronRight } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";

interface XpLog {
  id: string;
  amount: number;
  reason: string;
  timestamp: string;
  type?: "gain" | "conversion" | "bonus";
}

interface XpLogsProps {
  searchQuery?: string;
}

export default function XpLogs({ searchQuery = "" }: XpLogsProps) {
  const { user } = useAuth();
  const [logs, setLogs] = useState<XpLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const pageSize = 10;

  const fetchXpLogs = useCallback(async (page: number = 1) => {
    if (!user) return;

    try {
      setLoading(true);
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/pioneer/xp-logs?page=${page}&page_size=${pageSize}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch XP logs");
      }

      const data = await response.json();
      setLogs(data.logs || []);

      // Set pagination data
      if (data.pagination) {
        setTotalPages(data.pagination.total_pages || 1);
        setTotalItems(data.pagination.total || 0);
        setCurrentPage(data.pagination.page || 1);
      }
    } catch (error) {
      console.error("Error fetching XP logs:", error);
    } finally {
      setLoading(false);
    }
  }, [user, pageSize]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchXpLogs(page);
  };

  // Fetch logs on initial render
  useEffect(() => {
    if (user) {
      fetchXpLogs(1);
    }
  }, [user, fetchXpLogs]);

  // Reset to first page and fetch logs when search query changes
  useEffect(() => {
    if (user) {
      setCurrentPage(1);
      fetchXpLogs(1);
    }
  }, [searchQuery, user, fetchXpLogs]);

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-40" />
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Filter logs based on search query
  const filteredLogs = searchQuery.trim() === ""
    ? logs
    : logs.filter(log =>
        log.reason.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.amount.toString().includes(searchQuery)
      );

  // Render content based on logs state
  const renderContent = () => {
    if (logs.length === 0) {
      return (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">No XP activity yet. Start exploring to earn XP!</p>
          </CardContent>
        </Card>
      );
    }

    if (filteredLogs.length === 0 && searchQuery.trim() !== "") {
      return (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">No XP activity matches your search.</p>
          </CardContent>
        </Card>
      );
    }

    return (
      <>
        {filteredLogs.map((log) => (
        <Card
          key={log.id}
          className={
            log.type === "conversion" ? "border-yellow-500/20 bg-yellow-50/10" :
            log.type === "bonus" ? "border-green-500/20 bg-green-50/10" : ""
          }
        >
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base flex items-center gap-2">
                {log.type === "conversion" ? (
                  <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-900 px-2 py-1 rounded-full">
                    <div className="flex items-center gap-1">
                      <Sparkles className="h-4 w-4 text-yellow-500" />
                      <span className="text-yellow-500 font-medium">-{Math.abs(log.amount)} XP</span>
                      <span className="text-xs text-gray-500">(Debit)</span>
                    </div>
                    <ArrowRight className="h-3 w-3 text-muted-foreground" />
                    <div className="flex items-center gap-1">
                      <Coins className="h-4 w-4 text-green-500" />
                      <span className="text-green-500 font-medium">+{Math.abs(log.amount) / 10} Credits</span>
                      <span className="text-xs text-gray-500">(Credit)</span>
                    </div>
                  </div>
                ) : log.type === "bonus" ? (
                  <>
                    <Sparkles className="h-4 w-4 text-green-500" />
                    <span className="text-green-500">+{log.amount} XP Bonus</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 text-yellow-500" />
                    <span>+{log.amount} XP</span>
                  </>
                )}
              </CardTitle>
              <CardDescription>
                {log.timestamp ? formatDistanceToNow(new Date(log.timestamp), { addSuffix: true }) : ""}
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{log.reason}</p>
            {log.type === "conversion" && (
              <div className="mt-2 text-xs text-muted-foreground flex items-center gap-1">
                <Coins className="h-3 w-3" />
                <span>Credits can be used to access premium features</span>
              </div>
            )}
          </CardContent>
        </Card>
      ))}

        {/* Pagination */}
        {totalPages > 1 && (
          <Card>
            <CardFooter className="flex items-center justify-between py-4">
              <div className="text-sm text-muted-foreground">
                Showing {logs.length} of {totalItems} items
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-sm">
                  Page {currentPage} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardFooter>
          </Card>
        )}
      </>
    );
  };

  return (
    <div className="space-y-4">
      {renderContent()}
    </div>
  );
}
