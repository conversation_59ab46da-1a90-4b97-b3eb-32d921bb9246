"use client";

import { useState } from "react";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useAuth } from "@/hooks/use-auth";

interface ChangeRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: {
    uid: string;
    name?: string;
    email?: string;
    role?: string;
  };
  onRoleChanged: () => void;
}

export default function ChangeRoleDialog({
  open,
  onOpenChange,
  user,
  onRoleChanged,
}: ChangeRoleDialogProps) {
  const { user: currentUser } = useAuth();
  const [selectedRole, setSelectedRole] = useState<string>(user.role || "user");
  const [isAdmin, setIsAdmin] = useState<boolean>(user.role === "admin");
  const [loading, setLoading] = useState(false);

  const handleRoleChange = async () => {
    if (!currentUser) return;

    // Don't do anything if the role hasn't changed
    if (selectedRole === user.role) {
      onOpenChange(false);
      return;
    }

    setLoading(true);

    try {
      const token = await currentUser.getIdToken();

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/update-user-role`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          user_id: user.uid,
          role: selectedRole,
          is_admin: selectedRole === "admin" || isAdmin,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to update user role");
      }

      toast.success(`User role updated to ${selectedRole}`);
      onRoleChanged();
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating user role:", error);
      toast.error((error as Error).message || "Failed to update user role");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Change User Role</DialogTitle>
          <DialogDescription>
            Update the role for user {user.name || user.email || user.uid}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Current Role</label>
            <div className="px-3 py-2 rounded-md bg-muted text-sm">
              {user.role || "user"}
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">New Role</label>
            <Select
              value={selectedRole}
              onValueChange={setSelectedRole}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user">User</SelectItem>
                <SelectItem value="agent">Agent</SelectItem>
                <SelectItem value="brand">Brand</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2 pt-2">
            <Checkbox
              id="admin-status"
              checked={selectedRole === "admin" || isAdmin}
              onCheckedChange={(checked) => {
                setIsAdmin(checked as boolean);
              }}
              disabled={selectedRole === "admin" || loading}
            />
            <label
              htmlFor="admin-status"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Grant admin privileges
            </label>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button onClick={handleRoleChange} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              "Update Role"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
