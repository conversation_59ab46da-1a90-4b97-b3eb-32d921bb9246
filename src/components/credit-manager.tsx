'use client';

import { useCreditsContext } from '@/contexts/credits-context';
import { useEffect, useState, useCallback } from 'react';
import { toast } from 'sonner';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface CreditManagerProps {
  onInsufficientCredits?: () => void;
  requiredCredits?: number;
  autoDeduct?: boolean;
  showWarning?: boolean;
}

export function CreditManager({
  onInsufficientCredits,
  requiredCredits = 1,
  autoDeduct = false,
  showWarning = true,
}: CreditManagerProps) {
  const { credits, loading, updateCredits } = useCreditsContext();
  const [hasChecked, setHasChecked] = useState(false);
  const router = useRouter();

  const deductCredits = useCallback(async () => {
    if (credits === null || credits < requiredCredits) {
      if (onInsufficientCredits) {
        onInsufficientCredits();
      }
      return false;
    }

    try {
      const newCredits = credits - requiredCredits;
      const success = await updateCredits(newCredits);

      if (success) {
        return true;
      } else {
        toast.error('Failed to deduct credits');
        return false;
      }
    } catch (error) {
      console.error('Error deducting credits:', error);
      toast.error('Error processing credits');
      return false;
    }
  }, [credits, requiredCredits, onInsufficientCredits, updateCredits]);

  useEffect(() => {
    // Only check once when credits are loaded
    if (!loading && credits !== null && !hasChecked) {
      setHasChecked(true);

      if (credits < requiredCredits) {
        if (onInsufficientCredits) {
          onInsufficientCredits();
        }

        if (showWarning) {
          toast.error('Insufficient credits', {
            description: 'You need more credits to continue.',
          });
        }
      } else if (autoDeduct) {
        deductCredits();
      }
    }
  }, [credits, loading, hasChecked, requiredCredits, onInsufficientCredits, showWarning, autoDeduct, deductCredits]);

  if (loading || credits === null) {
    return null;
  }

  if (credits < requiredCredits && showWarning) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Insufficient Credits</AlertTitle>
        <AlertDescription className="flex flex-col gap-2">
          <p>You need at least {requiredCredits} credits to use this feature.</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/dashboard/user/payments')}
          >
            Get More Credits
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return null;
}
