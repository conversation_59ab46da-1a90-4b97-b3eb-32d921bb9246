'use client';

import { useCreditsContext } from '@/contexts/credits-context';
import { Loader2, Coins } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function CreditsDisplay() {
  const { credits, loading, error } = useCreditsContext();

  if (loading) {
    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Loading credits...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 text-sm text-destructive">
        <span>Error loading credits</span>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-2 text-sm font-medium cursor-help">
            <Coins className="h-4 w-4 text-yellow-500" />
            <span>{credits !== null ? (typeof credits === 'number' ? credits : String(credits)) : '--'} Credits</span>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Your available credits for using the platform</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
