import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Calendar,
  BarChart3,
  Download,
  RefreshCw,
  Clock,
  Target
} from "lucide-react";
import { GEOScoreCard } from "./GEOScoreCard";
import { GEORecommendationCard } from "./GEORecommendationCard";
import { formatDistanceToNow } from "date-fns";

interface HistoricalGEOAnalysis {
  analysisId: string;
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: Array<{
    url: string;
    title: string;
    summary: string;
    score: number;
  }>;
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: Array<{
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }>;
  analyzedAt: string;
  brandId: string;
  websiteAnalyzed: string;
  brandName: string;
  isHistorical: boolean;
  analysisVersion: string;
}

interface GEOHistoricalReportProps {
  analysis: HistoricalGEOAnalysis;
  onBack: () => void;
  onRunNewAnalysis: () => void;
  loading?: boolean;
}

export function GEOHistoricalReport({ 
  analysis, 
  onBack, 
  onRunNewAnalysis,
  loading = false 
}: GEOHistoricalReportProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      relative: formatDistanceToNow(date, { addSuffix: true }),
      absolute: date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const dateInfo = formatDate(analysis.analyzedAt);

  const exportToPDF = () => {
    // Create a simple HTML report for PDF generation
    const reportContent = `
      <html>
        <head>
          <title>Historical GEO Analysis Report - ${analysis.brandName}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              margin: 20px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 40px;
              padding-bottom: 20px;
              border-bottom: 2px solid #e5e7eb;
            }
            .score {
              font-size: 28px;
              font-weight: bold;
              color: #2563eb;
              margin-top: 15px;
            }
            .section {
              margin-bottom: 30px;
              page-break-inside: avoid;
            }
            .section h3 {
              color: #1f2937;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 8px;
              margin-bottom: 15px;
            }
            .metric {
              display: inline-block;
              margin: 8px;
              padding: 12px;
              border: 1px solid #d1d5db;
              border-radius: 8px;
              background: #f9fafb;
              min-width: 200px;
            }
            .recommendation {
              margin: 15px 0;
              padding: 15px;
              border-left: 4px solid #2563eb;
              background: #f8fafc;
              border-radius: 6px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .page {
              margin: 12px 0;
              padding: 12px;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              background: #fafafa;
            }
            .historical-note {
              background: #fef3c7;
              padding: 15px;
              border-radius: 8px;
              margin-bottom: 25px;
              border: 1px solid #f59e0b;
            }
            @media print {
              .section { page-break-inside: avoid; }
              .recommendation { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="historical-note">
            <strong>Historical Report:</strong> This analysis was generated on ${dateInfo.absolute}
          </div>
          
          <div class="header">
            <h1>GEO Analysis Report</h1>
            <h2>${analysis.brandName}</h2>
            <p>Analysis Date: ${dateInfo.absolute}</p>
            <p>Website: ${analysis.websiteAnalyzed}</p>
            <div class="score">Overall GEO Score: ${analysis.overallScore}/100</div>
          </div>

          <div class="section">
            <h3>Score Breakdown</h3>
            <div class="metric">Prompt Mention Rate: ${Math.round(analysis.promptMentionRate)}% (40% weight)</div>
            <div class="metric">Citation Rate: ${Math.round(analysis.citationRate)}% (20% weight)</div>
            <div class="metric">Website Optimization: ${analysis.websiteOptimization}/100 (30% weight)</div>
            <div class="metric">Sentiment/Tone: ${analysis.sentimentTone}/100 (10% weight)</div>
          </div>

          <div class="section">
            <h3>Analyzed Pages (${analysis.analyzedPages.length})</h3>
            ${analysis.analyzedPages.map(page => `
              <div class="page">
                <strong>${page.title}</strong> (Score: ${page.score}/100)<br>
                <small>${page.url}</small><br>
                ${page.summary}
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h3>Actionable GEO Recommendations</h3>
            <p style="margin-bottom: 15px; color: #666; font-style: italic;">
              These AI-powered recommendations were generated based on the analysis from ${dateInfo.absolute}.
            </p>
            ${analysis.recommendations.map((rec, index) => `
              <div class="recommendation" style="margin-bottom: 20px; page-break-inside: avoid;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <span style="background: ${rec.priority === 'high' ? '#dc2626' : rec.priority === 'medium' ? '#d97706' : '#16a34a'};
                               color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; margin-right: 10px;">
                    ${rec.priority.toUpperCase()}
                  </span>
                  <span style="background: #f3f4f6; padding: 2px 8px; border-radius: 12px; font-size: 12px; color: #374151;">
                    ${rec.category}
                  </span>
                </div>
                <h4 style="margin: 8px 0; font-size: 16px; color: #1f2937;">${index + 1}. ${rec.title}</h4>
                <p style="margin: 8px 0; line-height: 1.5; color: #4b5563;">${rec.description}</p>
                <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; border-left: 4px solid #0ea5e9; margin-top: 10px;">
                  <strong style="color: #0c4a6e;">Expected Impact:</strong> ${rec.impact}
                </div>
              </div>
            `).join('')}
          </div>
        </body>
      </html>
    `;

    // Create a new window and print
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(reportContent);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with back button and historical indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={onBack}
            variant="outline"
            size="sm"
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to History
          </Button>
          
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Historical GEO Report</h2>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span title={dateInfo.absolute}>Analyzed {dateInfo.relative}</span>
              <Badge variant="outline" className="ml-2">
                Historical
              </Badge>
            </div>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={exportToPDF}
            variant="outline"
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            Export PDF
          </Button>
          
          <Button
            onClick={onRunNewAnalysis}
            disabled={loading}
            className="gap-2"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <BarChart3 className="h-4 w-4" />
            )}
            {loading ? "Running..." : "Run New Analysis"}
          </Button>
        </div>
      </div>

      {/* Historical context alert */}
      <Card className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
        <CardContent className="flex items-start gap-3 pt-6">
          <Clock className="h-5 w-5 text-amber-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-200">
              Historical Analysis Report
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
              This report was generated on {dateInfo.absolute} for {analysis.websiteAnalyzed}. 
              The data reflects your website&apos;s GEO performance at that time and may not represent current status.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Results - Same structure as current analysis */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="pages">Pages Analyzed</TabsTrigger>
          <TabsTrigger value="queries">AI Queries</TabsTrigger>
          <TabsTrigger value="discoverability">AI Discoverability</TabsTrigger>
          <TabsTrigger value="content">Content Analysis</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Overall GEO Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className="text-4xl font-bold">
                  <span className={getScoreColor(analysis.overallScore)}>
                    {analysis.overallScore}
                  </span>
                  <span className="text-muted-foreground text-lg">/100</span>
                </div>
                <div className="flex-1">
                  <Progress value={analysis.overallScore} className="h-3" />
                </div>
                <Badge variant={getScoreBadgeVariant(analysis.overallScore)}>
                  {analysis.overallScore >= 80 ? "Excellent" : 
                   analysis.overallScore >= 60 ? "Good" : "Needs Improvement"}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Weighted Score Components */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <GEOScoreCard
              title="Prompt Mention Rate"
              score={Math.round(analysis.promptMentionRate)}
              description="40% weight - Brand appears in AI responses"
            />
            <GEOScoreCard
              title="Citation Rate"
              score={Math.round(analysis.citationRate)}
              description="20% weight - Website links in AI outputs"
            />
            <GEOScoreCard
              title="Website Optimization"
              score={analysis.websiteOptimization}
              description="30% weight - Content quality and structure"
            />
            <GEOScoreCard
              title="Sentiment/Tone"
              score={analysis.sentimentTone}
              description="10% weight - Positive brand framing"
            />
          </div>
        </TabsContent>

        <TabsContent value="pages" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Analyzed Pages ({analysis.analyzedPages.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analysis.analyzedPages.map((page, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm">{page.title}</h4>
                      <Badge variant={page.score >= 70 ? "default" : page.score >= 50 ? "secondary" : "destructive"}>
                        {page.score}/100
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">{page.url}</p>
                    <p className="text-sm">{page.summary}</p>
                  </div>
                ))}
                {analysis.analyzedPages.length === 0 && (
                  <p className="text-muted-foreground text-center py-8">
                    No pages were analyzed in this historical report.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="queries" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Query Simulation Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analysis.simulatedQueries.map((query, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm">&quot;{query.query}&quot;</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant={query.brand_mentioned ? "default" : "outline"}>
                          {query.brand_mentioned ? "Mentioned" : "Not Mentioned"}
                        </Badge>
                        {query.mention_context && (
                          <Badge variant={
                            query.mention_context === "positive" ? "default" :
                            query.mention_context === "neutral" ? "secondary" : "destructive"
                          }>
                            {query.mention_context}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Likelihood Score: {query.likelihood_score}/100
                    </div>
                    <p className="text-sm">{query.reasoning}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="discoverability" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  AI Mention Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Total Mentions</span>
                  <Badge variant="outline">{analysis.aiDiscoverability.mentions}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Sentiment</span>
                  <Badge variant={
                    analysis.aiDiscoverability.sentiment === "positive" ? "default" :
                    analysis.aiDiscoverability.sentiment === "neutral" ? "secondary" : "destructive"
                  }>
                    {analysis.aiDiscoverability.sentiment}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Queries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analysis.aiDiscoverability.topQueries.map((query, index) => (
                    <div key={index} className="text-sm p-2 bg-muted rounded">
                      &quot;{query}&quot;
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <GEOScoreCard
              title="Structure Score"
              score={analysis.contentOptimization.structureScore}
              description="Heading hierarchy and organization"
            />
            <GEOScoreCard
              title="Factual Claims"
              score={analysis.contentOptimization.factualClaimsScore}
              description="Evidence-based content"
            />
            <GEOScoreCard
              title="AI Readability"
              score={analysis.contentOptimization.aiReadabilityScore}
              description="Optimized for AI consumption"
            />
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          {analysis.recommendations.map((rec, index) => (
            <GEORecommendationCard
              key={index}
              recommendation={rec}
            />
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
