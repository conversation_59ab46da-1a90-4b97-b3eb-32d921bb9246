"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>R<PERSON>, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { BasicInfoStepProps, AGENT_TYPES } from "@/types/onboarding";

/**
 * BasicInfoStep component for collecting company and product information
 */
const BasicInfoStep = ({
  agentName,
  setAgentName,
  agentType,
  setAgentType,
  otherAgentType,
  setOtherAgentType,
  agentUrl,
  setAgentUrl,
  onNext,
  isLoading
}: BasicInfoStepProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <div className="mx-auto bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mb-4">
          <Bot className="w-8 h-8 text-primary" />
        </div>
        <h2 className="text-2xl font-bold">Company & Product Information</h2>
        <p className="text-muted-foreground">
          Tell us about your business and product
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="agentName">Company Name</Label>
          <Input
            id="agentName"
            value={agentName}
            onChange={(e) => setAgentName(e.target.value)}
            placeholder="e.g. Acme Inc., TechSolutions, AI Innovations"
            className="h-12"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="agentType">Product Type</Label>
          <Select value={agentType} onValueChange={setAgentType}>
            <SelectTrigger id="agentType" className="h-12">
              <SelectValue placeholder="Select product type" />
            </SelectTrigger>
            <SelectContent>
              {AGENT_TYPES.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {agentType === "Other" && (
          <div className="space-y-2">
            <Label htmlFor="otherAgentType">Please specify your product type</Label>
            <Input
              id="otherAgentType"
              value={otherAgentType}
              onChange={(e) => setOtherAgentType(e.target.value)}
              placeholder="e.g. Mobile App, Desktop App, Voice Assistant"
              className="h-12"
              required
            />
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="agentUrl">Website URL</Label>
          <Input
            id="agentUrl"
            value={agentUrl}
            onChange={(e) => setAgentUrl(e.target.value)}
            placeholder="https://yourtool.com"
            className="h-12"
            required
          />
          <p className="text-xs text-muted-foreground">
            This helps us understand where traffic is coming from and builds your trust score.
          </p>
        </div>
      </div>

      <Button
        onClick={onNext}
        disabled={isLoading}
        className="w-full h-12"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
          </>
        ) : (
          <>
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </>
        )}
      </Button>
    </motion.div>
  );
};

export default BasicInfoStep;
