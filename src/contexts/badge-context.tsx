"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { BadgeData } from "@/components/BadgeIcon";
import { toast } from "sonner";
import { Award } from "lucide-react";

interface BadgeContextType {
  unreadBadges: number;
  markBadgesAsRead: () => void;
  checkForNewBadges: () => Promise<void>;
  isCheckingBadges: boolean;
  lastAwardedBadge: BadgeData | null;
  setLastAwardedBadge: (badge: BadgeData | null) => void;
  showShareModal: (badge: BadgeData) => void;
}

const BadgeContext = createContext<BadgeContextType>({
  unreadBadges: 0,
  markBadgesAsRead: () => {},
  checkForNewBadges: async () => {},
  isCheckingBadges: false,
  lastAwardedBadge: null,
  setLastAwardedBadge: () => {},
  showShareModal: () => {},
});

export const useBadges = () => useContext(BadgeContext);

export const BadgeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [unreadBadges, setUnreadBadges] = useState<number>(0);
  const [isCheckingBadges, setIsCheckingBadges] = useState<boolean>(false);
  const [lastAwardedBadge, setLastAwardedBadge] = useState<BadgeData | null>(null);
  // We're using a static callback for now, but keeping the state for future extensibility
  const [shareModalCallback] = useState<((badge: BadgeData) => void) | null>(null);

  // Function to mark badges as read
  const markBadgesAsRead = () => {
    setUnreadBadges(0);
    // Store in localStorage that badges have been read
    if (typeof window !== 'undefined' && user) {
      localStorage.setItem(`admesh_badges_read_${user.uid}`, Date.now().toString());
    }
  };

  // Function to check for new badges
  const checkForNewBadges = useCallback(async () => {
    if (!user) return;

    try {
      setIsCheckingBadges(true);
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/badges/user/check`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to check for new badges");
      }

      const data = await response.json();

      if (data.count > 0) {
        // Update unread badges count
        setUnreadBadges(prev => prev + data.count);

        // Store the last awarded badge to show in modal if needed
        if (data.awarded_badges && data.awarded_badges.length > 0) {
          setLastAwardedBadge(data.awarded_badges[0]);

          // Show toast notification with share button
          toast.success(
            `You earned ${data.count > 1 ? `${data.count} new badges` : 'a new badge'}!`,
            {
              description: data.awarded_badges[0].metadata.name,
              icon: <Award className="h-4 w-4 text-yellow-500" />,
              action: {
                label: "Share",
                onClick: () => {
                  if (shareModalCallback) {
                    shareModalCallback(data.awarded_badges[0]);
                  }
                },
              },
              duration: 5000,
            }
          );
        }
      }
    } catch (error) {
      console.error("Error checking for badges:", error);
    } finally {
      setIsCheckingBadges(false);
    }
  }, [user, shareModalCallback, setUnreadBadges, setLastAwardedBadge, setIsCheckingBadges]);

  // Register the share modal callback
  const showShareModal = (badge: BadgeData) => {
    if (shareModalCallback) {
      shareModalCallback(badge);
    }
  };



  // Check for new badges on initial load
  useEffect(() => {
    if (user) {
      checkForNewBadges();
    }
  }, [user, checkForNewBadges]);

  return (
    <BadgeContext.Provider
      value={{
        unreadBadges,
        markBadgesAsRead,
        checkForNewBadges,
        isCheckingBadges,
        lastAwardedBadge,
        setLastAwardedBadge,
        showShareModal,
      }}
    >
      {children}
    </BadgeContext.Provider>
  );
};

// Component to register the share modal callback
export const ShareModalRegistration: React.FC<{ onShare: (badge: BadgeData) => void }> = ({ onShare }) => {
  // We only need the useBadges hook for the context, not for any specific values
  useBadges();

  useEffect(() => {
    // This is a hack to get around the context API limitations
    // We're using a global variable to store the callback
    (window as Window & { __showBadgeShareModal?: (badge: BadgeData) => void }).__showBadgeShareModal = (badge: BadgeData) => {
      onShare(badge);
    };

    return () => {
      delete (window as Window & { __showBadgeShareModal?: (badge: BadgeData) => void }).__showBadgeShareModal;
    };
  }, [onShare]);

  return null;
};
