import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth';

interface UseWalletReturn {
  walletBalance: number | null;
  loading: boolean;
  error: string | null;
  refreshWallet: () => Promise<void>;
  addFunds: (amount: number) => Promise<string | null>; // Returns checkout URL or null on error
}

export function useWallet(): UseWalletReturn {
  const { user, role } = useAuth();
  const [walletBalance, setWalletBalance] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWalletBalance = useCallback(async () => {
    if (!user || role !== 'brand') {
      setWalletBalance(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch wallet balance');
      }

      const data = await response.json();
      setWalletBalance(data.wallet_balance || 0);
    } catch (err) {
      console.error('Error fetching wallet balance:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [user, role]);

  const addFunds = useCallback(async (amount: number): Promise<string | null> => {
    if (!user || role !== 'brand' || amount <= 0) return null;

    try {
      setLoading(true);
      setError(null);

      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/create-checkout-session`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const data = await response.json();
      return data.session_url || null;
    } catch (err) {
      console.error('Error creating checkout session:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user, role]);

  // Initial fetch
  useEffect(() => {
    if (role === 'brand') {
      fetchWalletBalance();
    }
  }, [fetchWalletBalance, role]);

  return {
    walletBalance,
    loading,
    error,
    refreshWallet: fetchWalletBalance,
    addFunds,
  };
}
