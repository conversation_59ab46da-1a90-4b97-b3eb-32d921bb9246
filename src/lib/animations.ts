/**
 * Comprehensive Animation Library for Landing Pages
 * Provides smooth, consistent animations across all sections
 */

import { Variants } from "framer-motion";

// Animation duration constants
export const ANIMATION_DURATION = {
  fast: 0.3,
  normal: 0.5,
  slow: 0.8,
  verySlow: 1.2
};

// Easing functions
export const EASING = {
  easeOut: [0.0, 0.0, 0.2, 1],
  easeIn: [0.4, 0.0, 1, 1],
  easeInOut: [0.4, 0.0, 0.2, 1],
  spring: { type: "spring", stiffness: 100, damping: 15 },
  bouncy: { type: "spring", stiffness: 400, damping: 10 }
};

// Fade animations
export const fadeIn: Variants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  }
};

export const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 30 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  }
};

export const fadeInDown: Variants = {
  hidden: { opacity: 0, y: -30 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  }
};

export const fadeInLeft: Variants = {
  hidden: { opacity: 0, x: -30 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  }
};

export const fadeInRight: Variants = {
  hidden: { opacity: 0, x: 30 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  }
};

// Scale animations
export const scaleIn: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  }
};

export const scaleInBouncy: Variants = {
  hidden: { opacity: 0, scale: 0.3 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: EASING.bouncy
  }
};

// Slide animations
export const slideInUp: Variants = {
  hidden: { opacity: 0, y: 100 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: ANIMATION_DURATION.slow, ease: EASING.easeOut }
  }
};

export const slideInDown: Variants = {
  hidden: { opacity: 0, y: -100 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: ANIMATION_DURATION.slow, ease: EASING.easeOut }
  }
};

// Rotation animations
export const rotateIn: Variants = {
  hidden: { opacity: 0, rotate: -180 },
  visible: { 
    opacity: 1, 
    rotate: 0,
    transition: { duration: ANIMATION_DURATION.slow, ease: EASING.easeOut }
  }
};

// Container animations for staggered children
export const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

export const staggerContainerFast: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.05
    }
  }
};

export const staggerContainerSlow: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.2
    }
  }
};

// Hero section specific animations
export const heroTitle: Variants = {
  hidden: { opacity: 0, y: 50, scale: 0.9 },
  visible: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: { 
      duration: ANIMATION_DURATION.slow, 
      ease: EASING.easeOut,
      delay: 0.2
    }
  }
};

export const heroSubtitle: Variants = {
  hidden: { opacity: 0, y: 30 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: ANIMATION_DURATION.normal, 
      ease: EASING.easeOut,
      delay: 0.4
    }
  }
};

export const heroButtons: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: ANIMATION_DURATION.normal, 
      ease: EASING.easeOut,
      delay: 0.6
    }
  }
};

// Card animations
export const cardHover: Variants = {
  rest: { 
    scale: 1, 
    y: 0,
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
  },
  hover: { 
    scale: 1.02, 
    y: -5,
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: { duration: ANIMATION_DURATION.fast, ease: EASING.easeOut }
  }
};

export const cardSlideIn: Variants = {
  hidden: { opacity: 0, y: 50, scale: 0.9 },
  visible: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  }
};

// Button animations
export const buttonHover: Variants = {
  rest: { scale: 1, y: 0 },
  hover: { 
    scale: 1.05, 
    y: -2,
    transition: { duration: ANIMATION_DURATION.fast, ease: EASING.easeOut }
  },
  tap: { scale: 0.95 }
};

export const buttonPulse: Variants = {
  rest: { scale: 1 },
  pulse: { 
    scale: [1, 1.05, 1],
    transition: { 
      duration: 1.5, 
      repeat: Infinity, 
      ease: EASING.easeInOut 
    }
  }
};

// Icon animations
export const iconFloat: Variants = {
  rest: { y: 0, rotate: 0 },
  float: { 
    y: [-5, 5, -5],
    rotate: [0, 5, -5, 0],
    transition: { 
      duration: 3, 
      repeat: Infinity, 
      ease: EASING.easeInOut 
    }
  }
};

export const iconSpin: Variants = {
  rest: { rotate: 0 },
  spin: { 
    rotate: 360,
    transition: { 
      duration: 2, 
      repeat: Infinity, 
      ease: "linear" 
    }
  }
};

// Text animations
export const typewriter: Variants = {
  hidden: { width: 0 },
  visible: { 
    width: "auto",
    transition: { 
      duration: ANIMATION_DURATION.verySlow, 
      ease: "linear" 
    }
  }
};

export const textReveal: Variants = {
  hidden: { 
    opacity: 0,
    clipPath: "inset(0 100% 0 0)"
  },
  visible: { 
    opacity: 1,
    clipPath: "inset(0 0% 0 0)",
    transition: { 
      duration: ANIMATION_DURATION.slow, 
      ease: EASING.easeOut 
    }
  }
};

// Page transition animations
export const pageTransition: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { duration: ANIMATION_DURATION.normal, ease: EASING.easeOut }
  },
  exit: { 
    opacity: 0, 
    y: -20,
    transition: { duration: ANIMATION_DURATION.fast, ease: EASING.easeIn }
  }
};

// Utility functions for creating custom animations
export const createStaggeredAnimation = (
  baseAnimation: Variants,
  staggerDelay: number = 0.1
): Variants => ({
  hidden: baseAnimation.hidden,
  visible: {
    ...baseAnimation.visible,
    transition: {
      ...(typeof baseAnimation.visible === 'object' && 'transition' in baseAnimation.visible
        ? baseAnimation.visible.transition
        : {}),
      staggerChildren: staggerDelay
    }
  }
});

export const createDelayedAnimation = (
  baseAnimation: Variants,
  delay: number
): Variants => ({
  hidden: baseAnimation.hidden,
  visible: {
    ...baseAnimation.visible,
    transition: {
      ...(typeof baseAnimation.visible === 'object' && 'transition' in baseAnimation.visible
        ? baseAnimation.visible.transition
        : {}),
      delay
    }
  }
});

// Scroll-triggered animation helpers
export const getScrollAnimation = (inView: boolean, animation: Variants) => ({
  initial: "hidden",
  animate: inView ? "visible" : "hidden",
  variants: animation
});

// Intersection observer options for smooth animations
export const intersectionOptions = {
  threshold: 0.1,
  triggerOnce: true,
  rootMargin: "-50px 0px"
};
