import {
  Sparkles,
  Search,
  Compass,
  ThumbsUp,
  CheckCircle,
  MessageCircle,
  Award,
  Calendar,
  Flame,
  Gift,
  UserPlus,
  MessageSquare,
  Share2,
  Moon,
  Sunrise,
  Check,
  UserCheck,
  Crown,
  LucideIcon
} from "lucide-react";

// Map badge types to icons
export const BADGE_ICONS: Record<string, LucideIcon> = {
  agent_pioneer: Sparkles,
  first_discovery: Search,
  first_reward: Gift,
  power_explorer: Compass,
  top_recommender: ThumbsUp,
  conversion_master: CheckCircle,
  referral_champion: UserPlus,
  feedback_provider: MessageSquare,
  feedback_champion: MessageCircle,
  verified_email: Check,
  profile_complete: UserCheck,
  milestone_100: Award,
  milestone_500: Award,
  milestone_1000: Award,
  milestone_5000: Award,
  daily_streak: Flame,
  weekly_active: Calendar,
  night_owl: Moon,
  early_bird: Sunrise,
  weekend_warrior: Calendar,
  social_butterfly: Share2,
  super_agent: Crown,
  first_followup: MessageCircle,
  followup_50: MessageSquare,
  followup_100: MessageCircle
};

// Map badge types to colors
export const BADGE_COLORS: Record<string, string> = {
  agent_pioneer: "bg-yellow-500",
  first_discovery: "bg-indigo-500",
  first_reward: "bg-lime-500",
  power_explorer: "bg-sky-500",
  top_recommender: "bg-emerald-500",
  conversion_master: "bg-pink-500",
  referral_champion: "bg-emerald-700",
  feedback_provider: "bg-violet-600",
  feedback_champion: "bg-violet-500",
  verified_email: "bg-green-500",
  profile_complete: "bg-sky-500",
  milestone_100: "bg-gray-500",
  milestone_500: "bg-gray-600",
  milestone_1000: "bg-gray-700",
  milestone_5000: "bg-yellow-400",
  daily_streak: "bg-red-500",
  weekly_active: "bg-blue-500",
  night_owl: "bg-gray-600",
  early_bird: "bg-amber-500",
  weekend_warrior: "bg-violet-500",
  social_butterfly: "bg-sky-500",
  super_agent: "bg-amber-600",
  first_followup: "bg-indigo-500",
  followup_50: "bg-emerald-500",
  followup_100: "bg-amber-500"
};
