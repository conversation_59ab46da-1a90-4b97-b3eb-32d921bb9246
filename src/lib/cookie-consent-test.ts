/**
 * <PERSON>ie <PERSON>sent System Test Utilities
 * Use these functions to test the cookie consent system functionality
 */

import {
  getConsentStatus,
  saveConsentPreferences,
  clearConsentData,
  getDefaultPreferences,
  getAllAcceptedPreferences,
  hasRejectedAllCookies,
  initializeConsentSystem,
  getUserRegion,
  requiresExplicitConsent,
  getConsentBannerText,
  type CookiePreferences,
  // type ConsentStatus // Available for future use
} from './cookie-consent';

/**
 * Test suite for cookie consent functionality
 */
export class CookieConsentTester {
  private originalLocalStorage: Storage | null = null;
  private originalCookies: string = '';

  /**
   * Setup test environment
   */
  setup(): void {
    // Backup original state
    this.originalLocalStorage = window.localStorage;
    this.originalCookies = document.cookie;
    
    console.log('🧪 Cookie Consent Test Suite - Setup Complete');
  }

  /**
   * Cleanup test environment
   */
  cleanup(): void {
    // Clear test data
    clearConsentData();
    
    console.log('🧹 Cookie Consent Test Suite - Cleanup Complete');
  }

  /**
   * Test basic consent functionality
   */
  async testBasicConsent(): Promise<boolean> {
    console.log('🔍 Testing basic consent functionality...');
    
    try {
      // Clear any existing consent
      clearConsentData();
      
      // Check initial state
      const initialStatus = getConsentStatus();
      console.assert(!initialStatus.hasConsent, 'Initial state should have no consent');
      console.assert(initialStatus.shouldShowBanner, 'Banner should be shown initially');
      
      // Save preferences
      const testPreferences: CookiePreferences = {
        necessary: true,
        analytics: true,
        marketing: false,
        functional: true
      };
      
      saveConsentPreferences(testPreferences);
      
      // Check updated state
      const updatedStatus = getConsentStatus();
      console.assert(updatedStatus.hasConsent, 'Should have consent after saving');
      console.assert(!updatedStatus.shouldShowBanner, 'Banner should not be shown after consent');
      console.assert(updatedStatus.preferences?.analytics === true, 'Analytics preference should be saved');
      console.assert(updatedStatus.preferences?.marketing === false, 'Marketing preference should be saved');
      
      console.log('✅ Basic consent functionality test passed');
      return true;
    } catch (error) {
      console.error('❌ Basic consent functionality test failed:', error);
      return false;
    }
  }

  /**
   * Test consent expiration
   */
  async testConsentExpiration(): Promise<boolean> {
    console.log('🔍 Testing consent expiration...');
    
    try {
      // Clear any existing consent
      clearConsentData();
      
      // Save preferences
      saveConsentPreferences(getDefaultPreferences());
      
      // Manually expire consent by modifying the stored data
      const consentData = JSON.parse(localStorage.getItem('admesh-consent-data') || '{}');
      consentData.expiresAt = Date.now() - 1000; // Expired 1 second ago
      localStorage.setItem('admesh-consent-data', JSON.stringify(consentData));
      
      // Check if consent is properly detected as expired
      const status = getConsentStatus();
      console.assert(status.isExpired, 'Consent should be detected as expired');
      console.assert(status.shouldShowBanner, 'Banner should be shown for expired consent');
      
      console.log('✅ Consent expiration test passed');
      return true;
    } catch (error) {
      console.error('❌ Consent expiration test failed:', error);
      return false;
    }
  }

  /**
   * Test localStorage fallback to cookies
   */
  async testLocalStorageFallback(): Promise<boolean> {
    console.log('🔍 Testing localStorage fallback to cookies...');
    
    try {
      // Clear any existing consent
      clearConsentData();
      
      // Temporarily disable localStorage
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = () => {
        throw new Error('localStorage disabled for testing');
      };
      
      // Try to save preferences (should fallback to cookies)
      saveConsentPreferences(getAllAcceptedPreferences());
      
      // Restore localStorage
      localStorage.setItem = originalSetItem;
      
      // Check if consent was saved via cookies
      const cookieExists = document.cookie.includes('admesh_consent');
      console.assert(cookieExists, 'Consent should be saved in cookies when localStorage fails');
      
      console.log('✅ localStorage fallback test passed');
      return true;
    } catch (error) {
      console.error('❌ localStorage fallback test failed:', error);
      return false;
    }
  }

  /**
   * Test region detection
   */
  async testRegionDetection(): Promise<boolean> {
    console.log('🔍 Testing region detection...');
    
    try {
      const region = await getUserRegion();
      console.assert(['EU', 'CA', 'US', 'OTHER'].includes(region), 'Region should be one of the expected values');
      
      const requiresConsent = await requiresExplicitConsent();
      console.assert(typeof requiresConsent === 'boolean', 'Explicit consent requirement should be boolean');
      
      const bannerText = await getConsentBannerText();
      console.assert(bannerText.title.length > 0, 'Banner title should not be empty');
      console.assert(bannerText.description.length > 0, 'Banner description should not be empty');
      
      console.log('✅ Region detection test passed');
      return true;
    } catch (error) {
      console.error('❌ Region detection test failed:', error);
      return false;
    }
  }

  /**
   * Test preference validation
   */
  async testPreferenceValidation(): Promise<boolean> {
    console.log('🔍 Testing preference validation...');
    
    try {
      // Test default preferences
      const defaultPrefs = getDefaultPreferences();
      console.assert(defaultPrefs.necessary === true, 'Necessary cookies should always be true');
      console.assert(defaultPrefs.analytics === false, 'Analytics should be false by default');
      
      // Test all accepted preferences
      const allAccepted = getAllAcceptedPreferences();
      console.assert(allAccepted.necessary === true, 'Necessary cookies should always be true');
      console.assert(allAccepted.analytics === true, 'Analytics should be true when all accepted');
      console.assert(allAccepted.marketing === true, 'Marketing should be true when all accepted');
      console.assert(allAccepted.functional === true, 'Functional should be true when all accepted');
      
      // Test rejection detection
      saveConsentPreferences(getDefaultPreferences());
      const hasRejected = hasRejectedAllCookies();
      console.assert(hasRejected === true, 'Should detect rejection of all non-necessary cookies');
      
      console.log('✅ Preference validation test passed');
      return true;
    } catch (error) {
      console.error('❌ Preference validation test failed:', error);
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<{ passed: number; failed: number; total: number }> {
    console.log('🚀 Starting Cookie Consent Test Suite...');
    
    this.setup();
    
    const tests = [
      this.testBasicConsent,
      this.testConsentExpiration,
      this.testLocalStorageFallback,
      this.testRegionDetection,
      this.testPreferenceValidation
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
      try {
        const result = await test.call(this);
        if (result) {
          passed++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error('Test execution error:', error);
        failed++;
      }
    }
    
    this.cleanup();
    
    const total = tests.length;
    console.log(`\n📊 Test Results: ${passed}/${total} passed, ${failed}/${total} failed`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Cookie consent system is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please review the implementation.');
    }
    
    return { passed, failed, total };
  }
}

/**
 * Quick test function for development
 */
export async function quickTest(): Promise<void> {
  const tester = new CookieConsentTester();
  await tester.runAllTests();
}

/**
 * Manual test helpers for browser console
 */
export const testHelpers = {
  // Clear all consent data
  clearConsent: () => {
    clearConsentData();
    console.log('✅ Consent data cleared');
  },
  
  // Check current consent status
  checkStatus: () => {
    const status = getConsentStatus();
    console.log('📋 Current consent status:', status);
    return status;
  },
  
  // Accept all cookies
  acceptAll: () => {
    saveConsentPreferences(getAllAcceptedPreferences());
    console.log('✅ All cookies accepted');
  },
  
  // Reject all non-necessary cookies
  rejectAll: () => {
    saveConsentPreferences(getDefaultPreferences());
    console.log('✅ All non-necessary cookies rejected');
  },
  
  // Initialize system
  init: () => {
    const status = initializeConsentSystem();
    console.log('🔧 Consent system initialized:', status);
    return status;
  }
};

// Make test helpers available globally in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as unknown as Record<string, unknown>).cookieConsentTest = testHelpers;
  console.log('🛠️ Cookie consent test helpers available at window.cookieConsentTest');
}
