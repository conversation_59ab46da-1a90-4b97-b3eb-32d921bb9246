// lib/firebase.ts
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { config, getCurrentEnvironment } from "@/config/environment";

// Use environment-specific Firebase configuration
const firebaseConfig = config.firebase;

// Log Firebase configuration (only in development)
if (config.features.debugging) {
  console.log('🔥 Initializing Firebase with config:', {
    environment: config.environment,
    projectId: firebaseConfig.projectId,
    authDomain: firebaseConfig.authDomain
  });
}

// Validate Firebase configuration
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  throw new Error(`Missing Firebase configuration for ${getCurrentEnvironment()} environment`);
}

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);

// Export configuration for use in other parts of the app
export { config as environmentConfig };
