// Type definitions for onboarding components

// Original onboarding types
export interface Brand {
  website: string;
  brand_name: string;
  logo_url: string;
  work_email: string;
  headquarters: string;
  application_type: string;
}

export interface Product {
  title: string;
  url: string;
  description: string;
  categories: string[]; // Changed from single category to multiple categories
  keywords: string[];
  pricing_url: string;
  audience_segment: string;
  integration_list: string[];
  active_offers: string[];
  inactive_offers: string[];
}

export interface OfferIncentive {
  type: "discount" | "bonus" | "free_trial" | "credit" | "extended_plan";
  headline: string;
  details: string;
  cta_label: string;
}

export interface Offer {
  goal: string;
  model: string;
  payout_amount: string;
  budget: string;
  promo_applied: boolean; // Flag to track if promo credit has been applied
  offer_incentive?: OfferIncentive; // Optional offer incentive
}

export interface Tracking {
  method: string;
  webhook_url: string;
  notes: string;
  redirect_url: string;
  target_urls: string[];
}

// New onboarding component types
export interface BasicInfoStepProps {
  agentName: string;
  setAgentName: (value: string) => void;
  agentType: string;
  setAgentType: (value: string) => void;
  otherAgentType: string;
  setOtherAgentType: (value: string) => void;
  agentUrl: string;
  setAgentUrl: (value: string) => void;
  onNext: () => void;
  isLoading: boolean;
}

// API Key information
export interface ApiKeyInfo {
  key: string;
  id: string;
}

export interface ApiCredentialsStepProps {
  agentId: string;
  testApiKey: string;
  productionApiKey: string;
  onComplete: () => void;
  isLoading: boolean;
  // Optional callbacks for updating API keys
  onTestKeyGenerated?: (keyInfo: ApiKeyInfo) => void;
  onProductionKeyGenerated?: (keyInfo: ApiKeyInfo) => void;
}

export interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

export interface TestApiResponse {
  [key: string]: unknown;
}

// Agent Types
export const AGENT_TYPES = [
  "Chatbot",
  "GPT Plugin",
  "Chrome Extension",
  "SaaS App",
  "Other"
];
